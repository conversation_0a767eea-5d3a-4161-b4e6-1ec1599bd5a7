import http from "@/lib/http";

export interface ExportOptions {
  format?: 'excel' | 'csv';
  startDate?: string;
  endDate?: string;
  includeMembers?: boolean;
  includeFiles?: boolean;
  includeAnnouncements?: boolean;
  includeReports?: boolean;
}

const departmentImportExportApiRequest = {
  // Export department members
  exportMembers: (departmentId: string, options: ExportOptions, sessionToken: string) => {
    const queryParams = new URLSearchParams();
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    
    return http.get<Blob>(
      `/api/administrator/departments/${departmentId}/export/members?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
        responseType: 'blob' as any,
      }
    );
  },

  // Export department reports
  exportReports: (departmentId: string, options: ExportOptions, sessionToken: string) => {
    const queryParams = new URLSearchParams();
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    
    return http.get<Blob>(
      `/api/administrator/departments/${departmentId}/export/reports?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
        responseType: 'blob' as any,
      }
    );
  },

  // Export comprehensive department data
  exportDepartmentData: (departmentId: string, options: ExportOptions, sessionToken: string) => {
    const queryParams = new URLSearchParams();
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    
    return http.get<Blob>(
      `/api/administrator/departments/${departmentId}/export/data?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
        responseType: 'blob' as any,
      }
    );
  },

  // Helper function to download blob as file
  downloadBlob: (blob: Blob, filename: string) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  // Helper function to get filename from response headers
  getFilenameFromResponse: (response: any): string => {
    const contentDisposition = response.headers?.['content-disposition'];
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        return filenameMatch[1];
      }
    }
    return `export-${Date.now()}.xlsx`;
  }
};

export default departmentImportExportApiRequest;
