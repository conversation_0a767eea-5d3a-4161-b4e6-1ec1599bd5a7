const mongoose = require("mongoose");
const Department = require("../models/department");
const User = require("../models/user");
const { ForbiddenError } = require("@casl/ability");
const defineAbilityFor = require("../permissions/abilities");
const bcrypt = require('bcrypt');

// Get all departments
exports.getAllDepartments = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    // Only system admins can view all departments
    if (req.user.systemRole !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Only system administrators can view all departments"
      });
    }

    const { page = 1, perPage = 10, includeDeleted = false } = req.body;

    // Build query filter
    const filter = includeDeleted ? {} : { isActive: true };

    const onTotal = Department.countDocuments(filter);
    const onDepartments = Department.find(filter)
      .populate('departmentAdmin', 'username email')
      .populate('memberCount')
      .sort({ isActive: -1, name: 1 }) // Show active departments first
      .skip(perPage * page - perPage)
      .limit(perPage);

    const [total, departments] = await Promise.all([onTotal, onDepartments]);

    res.json({
      success: true,
      departments,
      total,
      page,
      perPage
    });

  } catch (error) {
    console.error('Error getting departments:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Get single department
exports.getDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    
    const department = await Department.findById(id)
      .populate('departmentAdmin', 'username email')
      .populate('parentDepartment', 'name code')
      .populate('subDepartments');

    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Check permissions
    if (req.user.systemRole !== 'admin' &&
        (!req.user.department || !req.user.department.equals(department._id))) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Get department members
    const members = await User.findByDepartment(department._id);

    res.json({
      success: true,
      department: {
        ...department.toObject(),
        members
      }
    });

  } catch (error) {
    console.error('Error getting department:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Create department
exports.createDepartment = async (req, res) => {
  try {
    // Only system admins can create departments
    if (req.user.systemRole !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Only system administrators can create departments"
      });
    }

    const { name, description, code, defaultPermissions, maxMembers, parentDepartment } = req.body;

    // Validate required fields
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: "Department name and code are required"
      });
    }

    // Check if department with same name or code exists
    const existingDept = await Department.findOne({
      $or: [
        { name: name.trim() },
        { code: code.toUpperCase().trim() }
      ]
    });

    if (existingDept) {
      return res.status(400).json({
        success: false,
        message: "Department with this name or code already exists"
      });
    }

    const department = new Department({
      name: name.trim(),
      description: description?.trim() || "",
      code: code.toUpperCase().trim(),
      defaultPermissions: defaultPermissions || [],
      maxMembers: maxMembers || 100,
      parentDepartment: parentDepartment || null
    });

    await department.save();

    res.status(201).json({
      success: true,
      department,
      message: "Department created successfully"
    });

  } catch (error) {
    console.error('Error creating department:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Department name or code already exists"
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Update department
exports.updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, defaultPermissions, maxMembers, isActive } = req.body;

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Check permissions
    if (req.user.systemRole !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Only system administrators can update departments"
      });
    }

    // Update fields
    if (name) department.name = name.trim();
    if (description !== undefined) department.description = description.trim();
    if (defaultPermissions) department.defaultPermissions = defaultPermissions;
    if (maxMembers) department.maxMembers = maxMembers;
    if (isActive !== undefined) department.isActive = isActive;

    await department.save();

    res.json({
      success: true,
      department,
      message: "Department updated successfully"
    });

  } catch (error) {
    console.error('Error updating department:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Assign department admin
exports.assignDepartmentAdmin = async (req, res) => {
  try {
    const { departmentId, userId } = req.body;

    // Only system admins can assign department admins
    if (req.user.systemRole !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Only system administrators can assign department admins"
      });
    }

    const department = await Department.findById(departmentId);
    const user = await User.findById(userId);

    if (!department || !user) {
      return res.status(404).json({
        success: false,
        message: "Department or user not found"
      });
    }

    // User must be in the department
    if (!user.department || !user.department.equals(departmentId)) {
      return res.status(400).json({
        success: false,
        message: "User must be a member of the department"
      });
    }

    // Remove admin role from previous admin if exists
    if (department.departmentAdmin) {
      await User.findByIdAndUpdate(department.departmentAdmin, {
        isDepartmentAdmin: false
      });
    }

    // Assign new admin
    department.departmentAdmin = userId;
    user.isDepartmentAdmin = true;

    await Promise.all([department.save(), user.save()]);

    res.json({
      success: true,
      message: "Department admin assigned successfully"
    });

  } catch (error) {
    console.error('Error assigning department admin:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Get department members
exports.getDepartmentMembers = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, perPage = 10 } = req.query;

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Check permissions
    if (req.user.systemRole !== 'admin' &&
        (!req.user.department || !req.user.department.equals(department._id))) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    const onTotal = User.countDocuments({ 
      department: id, 
      private: false 
    });
    
    const onMembers = User.find({ 
      department: id, 
      private: false 
    })
    .select("_id username email isDepartmentAdmin permissions createdAt")
    .sort({ isDepartmentAdmin: -1, username: 1 })
    .skip(perPage * page - perPage)
    .limit(perPage);

    const [total, members] = await Promise.all([onTotal, onMembers]);

    res.json({
      success: true,
      members,
      total,
      department: {
        _id: department._id,
        name: department.name,
        code: department.code
      }
    });

  } catch (error) {
    console.error('Error getting department members:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Get available users for department
exports.getAvailableUsers = async (req, res) => {
  try {
    const { id } = req.params;
    const { search = "" } = req.query;
    const ability = defineAbilityFor(req.user);

    // Check permission - either system admin or department admin
    const canManage = ability.can("manage", "department") ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === id);

    if (!canManage) {
      return res.status(403).json({
        success: false,
        message: "Không có quyền truy cập"
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Build search query
    const searchQuery = {
      $and: [
        // Not already in this department
        {
          $or: [
            { department: { $ne: id } },
            { department: { $exists: false } }
          ]
        },
        // Not system admin (they shouldn't be in departments)
        { systemRole: { $ne: "admin" } },
        // Search by username or email
        search ? {
          $or: [
            { username: { $regex: search, $options: "i" } },
            { email: { $regex: search, $options: "i" } }
          ]
        } : {}
      ]
    };

    const users = await User.find(searchQuery)
      .select("username email systemRole department isDepartmentAdmin")
      .populate("department", "name")
      .limit(20)
      .sort({ username: 1 });

    res.json({
      success: true,
      users
    });

  } catch (error) {
    console.error("Error getting available users:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tìm kiếm người dùng"
    });
  }
};

// Add members to department
exports.addMembers = async (req, res) => {
  try {
    const { id } = req.params;
    const { userIds } = req.body;
    const ability = defineAbilityFor(req.user);

    // Check permission - either system admin or department admin
    const canManage = ability.can("manage", "department") ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === id);

    if (!canManage) {
      return res.status(403).json({
        success: false,
        message: "Không có quyền truy cập"
      });
    }

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Danh sách người dùng không hợp lệ"
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if department has space
    const currentMemberCount = await User.countDocuments({ department: id });
    if (currentMemberCount + userIds.length > department.maxMembers) {
      return res.status(400).json({
        success: false,
        message: `Phòng ban chỉ còn ${department.maxMembers - currentMemberCount} chỗ trống`
      });
    }

    // Validate users exist and are available
    const users = await User.find({
      _id: { $in: userIds },
      $or: [
        { department: { $ne: id } },
        { department: { $exists: false } }
      ],
      systemRole: { $ne: "admin" }
    });

    if (users.length !== userIds.length) {
      return res.status(400).json({
        success: false,
        message: "Một số người dùng không hợp lệ hoặc đã thuộc phòng ban khác"
      });
    }

    // Add users to department
    await User.updateMany(
      { _id: { $in: userIds } },
      {
        department: id,
        permissions: department.defaultPermissions || []
      }
    );

    res.json({
      success: true,
      message: `Đã thêm ${userIds.length} thành viên vào phòng ban`
    });

  } catch (error) {
    console.error("Error adding members:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi thêm thành viên"
    });
  }
};

// Remove member from department
exports.removeMember = async (req, res) => {
  try {
    const { id, userId } = req.params;
    const ability = defineAbilityFor(req.user);

    // Check permission - either system admin or department admin
    const canManage = ability.can("manage", "department") ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === id);

    if (!canManage) {
      return res.status(403).json({
        success: false,
        message: "Không có quyền truy cập"
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy người dùng"
      });
    }

    if (user.department?.toString() !== id) {
      return res.status(400).json({
        success: false,
        message: "Người dùng không thuộc phòng ban này"
      });
    }

    // Cannot remove department admin
    if (user.isDepartmentAdmin && department.departmentAdmin?.toString() === userId) {
      return res.status(400).json({
        success: false,
        message: "Không thể xóa quản lý phòng ban. Vui lòng chỉ định quản lý mới trước."
      });
    }

    // Remove user from department
    await User.findByIdAndUpdate(userId, {
      $unset: { department: 1 },
      isDepartmentAdmin: false,
      permissions: []
    });

    res.json({
      success: true,
      message: "Đã xóa thành viên khỏi phòng ban"
    });

  } catch (error) {
    console.error("Error removing member:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi xóa thành viên"
    });
  }
};

// Create new member for department
exports.createMember = async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, password, isDepartmentAdmin = false, permissions = [] } = req.body;
    const ability = defineAbilityFor(req.user);

    // Check permission - either system admin or department admin
    const canManage = ability.can("manage", "department") ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === id);

    if (!canManage) {
      return res.status(403).json({
        success: false,
        message: "Không có quyền truy cập"
      });
    }

    // Validate input
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        message: "Vui lòng điền đầy đủ thông tin"
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: "Mật khẩu phải có ít nhất 6 ký tự"
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if department has space
    const currentMemberCount = await User.countDocuments({ department: id });
    if (currentMemberCount >= department.maxMembers) {
      return res.status(400).json({
        success: false,
        message: "Phòng ban đã đầy"
      });
    }

    // Check if username or email already exists
    const existingUser = await User.findOne({
      $or: [
        { username: username.trim() },
        { email: email.trim() }
      ]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "Tên người dùng hoặc email đã tồn tại"
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = new User({
      username: username.trim(),
      email: email.trim(),
      password: hashedPassword,
      department: id,
      isDepartmentAdmin: isDepartmentAdmin,
      permissions: permissions.length > 0 ? permissions : department.defaultPermissions || [],
      systemRole: null, // Department users have null systemRole
      isActive: true,
      emailVerified: true // Auto-verify for department members
    });

    await newUser.save();

    // If this is a department admin, update department
    if (isDepartmentAdmin) {
      await Department.findByIdAndUpdate(id, {
        departmentAdmin: newUser._id
      });
    }

    res.json({
      success: true,
      message: "Tạo thành viên thành công",
      user: {
        _id: newUser._id,
        username: newUser.username,
        email: newUser.email,
        isDepartmentAdmin: newUser.isDepartmentAdmin
      }
    });

  } catch (error) {
    console.error("Error creating member:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo thành viên"
    });
  }
};

// Delete department (soft delete)
exports.deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    // Only system admins can delete departments
    if (req.user.systemRole !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Only system administrators can delete departments"
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    if (!department.isActive) {
      return res.status(400).json({
        success: false,
        message: "Department is already deleted"
      });
    }

    // Check if department has members
    const memberCount = await User.countDocuments({
      department: id,
      private: false
    });

    if (memberCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete department with ${memberCount} members. Please move or remove all members first.`
      });
    }

    // Check if department has sub-departments
    const subDepartmentCount = await Department.countDocuments({
      parentDepartment: id,
      isActive: true
    });

    if (subDepartmentCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete department with ${subDepartmentCount} sub-departments. Please delete or move sub-departments first.`
      });
    }

    // Soft delete the department
    department.isActive = false;
    department.deletedAt = new Date();
    await department.save();

    res.json({
      success: true,
      message: "Department deleted successfully"
    });

  } catch (error) {
    console.error('Error deleting department:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Restore deleted department
exports.restoreDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    // Only system admins can restore departments
    if (req.user.systemRole !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Only system administrators can restore departments"
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    if (department.isActive) {
      return res.status(400).json({
        success: false,
        message: "Department is not deleted"
      });
    }

    // Check if department name/code conflicts with existing active departments
    const existingDept = await Department.findOne({
      _id: { $ne: id },
      isActive: true,
      $or: [
        { name: department.name },
        { code: department.code }
      ]
    });

    if (existingDept) {
      return res.status(400).json({
        success: false,
        message: "Cannot restore: Department name or code conflicts with existing department"
      });
    }

    // Restore the department
    department.isActive = true;
    department.deletedAt = undefined;
    await department.save();

    res.json({
      success: true,
      message: "Department restored successfully",
      department
    });

  } catch (error) {
    console.error('Error restoring department:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};
