const mongoose = require('mongoose');
require('dotenv').config();

mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function checkSetting() {
  try {
    const db = mongoose.connection.db;
    const settings = await db.collection('settings').find({}).toArray();
    
    console.log('Settings found:', settings.length);
    settings.forEach(s => {
      console.log('Logo:', JSON.stringify(s.logo, null, 2));
    });
    
    // Also check files
    const files = await db.collection('files').find({}).toArray();
    console.log('\nFiles found:', files.length);
    files.forEach(f => {
      console.log('File:', f.filename, '| RelativePath:', f.relativePath, '| URL:', f.url);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

mongoose.connection.on('connected', () => {
  console.log('Connected to MongoDB');
  checkSetting();
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB error:', err);
  process.exit(1);
});
