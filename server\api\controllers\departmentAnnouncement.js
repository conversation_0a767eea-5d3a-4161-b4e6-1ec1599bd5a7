const mongoose = require("mongoose");
const DepartmentAnnouncement = require("../models/departmentAnnouncement");
const Department = require("../models/department");
const User = require("../models/user");
const { ForbiddenError } = require("@casl/ability");
const defineAbilityFor = require("../permissions/abilities");

// Get all announcements for a department
exports.getDepartmentAnnouncements = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { 
      page = 1, 
      perPage = 10, 
      status, 
      type, 
      priority, 
      tags,
      activeOnly = false 
    } = req.query;
    
    const ability = defineAbilityFor(req.user);

    // Check if user can access department announcements
    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canAccess = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId) ||
                     (req.user.department?.toString() === departmentId);

    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Build query options
    const options = {};
    if (status) options.status = status;
    if (type) options.type = type;
    if (priority) options.priority = priority;
    if (tags) options.tags = tags.split(',');
    if (activeOnly === 'true') options.activeOnly = true;

    const query = DepartmentAnnouncement.findByDepartment(departmentId, options);
    
    const onTotal = DepartmentAnnouncement.countDocuments({
      department: departmentId,
      ...(status && { status }),
      ...(type && { type }),
      ...(priority && { priority })
    });

    const onAnnouncements = query
      .skip(perPage * page - perPage)
      .limit(perPage);

    const [total, announcements] = await Promise.all([onTotal, onAnnouncements]);

    res.json({
      success: true,
      announcements,
      total,
      page: parseInt(page),
      perPage: parseInt(perPage),
      totalPages: Math.ceil(total / perPage)
    });

  } catch (error) {
    console.error('Error getting department announcements:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Get single announcement
exports.getAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;

    const announcement = await DepartmentAnnouncement.findById(id)
      .populate('department', 'name code')
      .populate('author', 'username email')
      .populate('lastEditedBy', 'username email')
      .populate('specificTargets', 'username email')
      .populate('views.user', 'username email')
      .populate('likes.user', 'username email')
      .populate('comments.user', 'username email')
      .populate('acknowledgments.user', 'username email');

    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: "Announcement not found"
      });
    }

    // Check access permission
    const canAccess = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === announcement.department._id.toString()) ||
                     (req.user.department?.toString() === announcement.department._id.toString());

    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Track view if user is a department member
    if (req.user.department?.toString() === announcement.department._id.toString()) {
      await announcement.addView(
        req.user._id, 
        req.ip || req.connection.remoteAddress,
        req.get('User-Agent')
      );
    }

    res.json({
      success: true,
      announcement
    });

  } catch (error) {
    console.error('Error getting announcement:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Create new announcement
exports.createAnnouncement = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { 
      title, 
      content, 
      summary,
      type = "general",
      priority = "normal",
      targetAudience = "all",
      specificTargets = [],
      expiresAt,
      scheduledFor,
      allowComments = true,
      allowLikes = true,
      requireAcknowledgment = false,
      sendNotification = true,
      tags = []
    } = req.body;

    // Validate required fields
    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: "Title and content are required"
      });
    }

    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canCreate = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId);

    if (!canCreate) {
      return res.status(403).json({
        success: false,
        message: "Only department admins can create announcements"
      });
    }

    const announcement = new DepartmentAnnouncement({
      department: departmentId,
      title: title.trim(),
      content: content.trim(),
      summary: summary?.trim(),
      type,
      priority,
      targetAudience,
      specificTargets: targetAudience === 'specific' ? specificTargets : [],
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined,
      author: req.user._id,
      allowComments,
      allowLikes,
      requireAcknowledgment,
      sendNotification,
      tags,
      status: scheduledFor ? 'draft' : 'published'
    });

    await announcement.save();

    // Populate the saved announcement
    await announcement.populate([
      { path: 'department', select: 'name code' },
      { path: 'author', select: 'username email' }
    ]);

    res.status(201).json({
      success: true,
      announcement,
      message: "Announcement created successfully"
    });

  } catch (error) {
    console.error('Error creating announcement:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Update announcement
exports.updateAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      title, 
      content, 
      summary,
      type,
      priority,
      targetAudience,
      specificTargets,
      expiresAt,
      scheduledFor,
      allowComments,
      allowLikes,
      requireAcknowledgment,
      tags,
      status
    } = req.body;

    const announcement = await DepartmentAnnouncement.findById(id);
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: "Announcement not found"
      });
    }

    // Permission check - only author or department admin can update
    const canUpdate = req.user.systemRole === 'admin' ||
                     announcement.author.equals(req.user._id) ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === announcement.department.toString());

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Update fields
    if (title) announcement.title = title.trim();
    if (content) announcement.content = content.trim();
    if (summary !== undefined) announcement.summary = summary?.trim();
    if (type) announcement.type = type;
    if (priority) announcement.priority = priority;
    if (targetAudience) announcement.targetAudience = targetAudience;
    if (specificTargets) announcement.specificTargets = targetAudience === 'specific' ? specificTargets : [];
    if (expiresAt !== undefined) announcement.expiresAt = expiresAt ? new Date(expiresAt) : undefined;
    if (scheduledFor !== undefined) announcement.scheduledFor = scheduledFor ? new Date(scheduledFor) : undefined;
    if (allowComments !== undefined) announcement.allowComments = allowComments;
    if (allowLikes !== undefined) announcement.allowLikes = allowLikes;
    if (requireAcknowledgment !== undefined) announcement.requireAcknowledgment = requireAcknowledgment;
    if (tags) announcement.tags = tags;
    if (status) announcement.status = status;

    // Track who edited
    announcement.lastEditedBy = req.user._id;

    await announcement.save();

    res.json({
      success: true,
      announcement,
      message: "Announcement updated successfully"
    });

  } catch (error) {
    console.error('Error updating announcement:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Delete announcement
exports.deleteAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;

    const announcement = await DepartmentAnnouncement.findById(id);
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: "Announcement not found"
      });
    }

    // Permission check - only author or department admin can delete
    const canDelete = req.user.systemRole === 'admin' ||
                     announcement.author.equals(req.user._id) ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === announcement.department.toString());

    if (!canDelete) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    await DepartmentAnnouncement.findByIdAndDelete(id);

    res.json({
      success: true,
      message: "Announcement deleted successfully"
    });

  } catch (error) {
    console.error('Error deleting announcement:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

module.exports = {
  getDepartmentAnnouncements: exports.getDepartmentAnnouncements,
  getAnnouncement: exports.getAnnouncement,
  createAnnouncement: exports.createAnnouncement,
  updateAnnouncement: exports.updateAnnouncement,
  deleteAnnouncement: exports.deleteAnnouncement
};
