'use client'

import authApiRequest from '@/apiRequests/auth'
import { useAppContext } from '@/app/app-provider'
import { Button } from '@/components/ui/button'
import { handleErrorApi } from '@/lib/utils'
import { usePathname, useRouter } from 'next/navigation'
import { toast } from "react-toastify";

export default function ButtonLogout() {
  const { setUser } = useAppContext()
  const router = useRouter()
  const pathname = usePathname()
  const handleLogout = async () => {
    try {
      // Clear local storage first
      localStorage.removeItem("user");
      localStorage.removeItem('sessionToken')
      localStorage.removeItem('sessionTokenExpiresAt')

      // Call logout API to clear server-side cookies
      await authApiRequest.logoutFromNextClientToNextServer()

      // Clear app state
      setUser(null)

      // Show success message
      toast.success("Đăng xuất thành công!");

      // Redirect to login with logout flag to bypass middleware redirect
      window.location.href = '/login?logout=true';

    } catch (error) {
      console.error('Logout error:', error)

      // Force logout even if API call fails
      try {
        await authApiRequest.logoutFromNextClientToNextServer(true)
      } catch (forceError) {
        console.error('Force logout error:', forceError)
      }

      // Clear everything anyway
      localStorage.removeItem("user");
      localStorage.removeItem('sessionToken')
      localStorage.removeItem('sessionTokenExpiresAt')
      setUser(null)

      // Redirect with force flag
      window.location.href = '/login?force=true';
    }
  }
  return (
    <button className="btn btn-sm btn-primary my-4" onClick={handleLogout}>
      Đăng xuất
    </button>
  )
}