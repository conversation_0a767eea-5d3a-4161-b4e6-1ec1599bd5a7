# Hệ Thống Phòng Ban (Department-Based System)

## Tổng Quan

Hệ thống đã được chuyển đổi từ quản lý theo role rờ<PERSON> rạc sang hệ thống quản lý theo phòng ban với cấu trúc phân quyền rõ ràng hơn.

## Cấu Trúc Mới

### 1. <PERSON><PERSON> Cấp Quyền

```
Quản Trị Hệ Thống (System Admin)
├── Quản lý tất cả phòng ban
├── Tạo/sửa/xóa phòng ban
├── Chỉ định Admin phòng ban
└── Truy cập toàn bộ hệ thống

Admin Phòng Ban (Department Admin)
├── Quản lý thành viên trong phòng ban
├── Phân quyền cho thành viên phòng ban
├── Quản lý tài nguyên phòng ban
└── Chỉ truy cập trong phạm vi phòng ban

Thành Viên Phòng Ban (Department User)
├── Quyền được cấp từ phòng ban
├── Truy cập tài nguyên phòng ban
└── Không thể quản lý người khác
```

### 2. Cấu Trúc Database

#### User Model (Cập nhật)
```javascript
{
  // Thông tin cơ bản
  username: String,
  email: String,
  password: String,
  
  // Hệ thống phân quyền mới
  systemRole: String, // 'admin' hoặc null
  department: ObjectId, // Tham chiếu đến Department
  isDepartmentAdmin: Boolean,
  permissions: [String], // Danh sách quyền cụ thể
  
  // Legacy (để tương thích ngược)
  rule: String // 'admin', 'manager', 'editor', 'user'
}
```

#### Department Model (Mới)
```javascript
{
  name: String, // Tên phòng ban
  code: String, // Mã phòng ban (unique)
  description: String,
  defaultPermissions: [String], // Quyền mặc định cho thành viên
  isActive: Boolean,
  departmentAdmin: ObjectId, // Tham chiếu đến User
  maxMembers: Number,
  parentDepartment: ObjectId, // Phòng ban cha (tùy chọn)
  level: Number // Cấp độ phòng ban
}
```

## Migration (Chuyển Đổi)

### 1. Chạy Migration Script

```bash
cd server/api/scripts
node migrate-to-departments.js
```

Script này sẽ:
- Tạo các phòng ban mặc định dựa trên role hiện tại
- Chuyển đổi users sang hệ thống phòng ban
- Cập nhật permissions
- Tạo indexes cần thiết

### 2. Phòng Ban Mặc Định

| Role Cũ | Phòng Ban Mới | Quyền Mặc Định |
|---------|---------------|----------------|
| admin | Quản Trị Hệ Thống | system_admin_full_access |
| manager | Quản Lý | dept_user_*, dept_file_*, dept_analytics_view |
| editor | Biên Tập | dept_user_view, dept_file_*, dept_analytics_view |
| user | Nhân Viên | dept_file_view, dept_file_upload |

### 3. Kiểm Tra Migration

```bash
cd server/api/scripts
node test-department-system.js
```

## Permissions Mới

### System Permissions (Chỉ System Admin)
- `system_admin_full_access` - Truy cập toàn bộ hệ thống
- `system_departments_manage` - Quản lý phòng ban
- `system_users_manage` - Quản lý tất cả users
- `system_settings_manage` - Cài đặt hệ thống
- `system_analytics_view` - Xem báo cáo toàn hệ thống

### Department Permissions
- `dept_user_view` - Xem thành viên phòng ban
- `dept_user_add` - Thêm thành viên
- `dept_user_edit` - Sửa thông tin thành viên
- `dept_user_delete` - Xóa thành viên
- `dept_file_view` - Xem file phòng ban
- `dept_file_upload` - Tải lên file
- `dept_file_delete` - Xóa file
- `dept_analytics_view` - Xem báo cáo phòng ban
- `dept_reports_*` - Quản lý báo cáo
- `dept_announcements_*` - Quản lý thông báo
- `dept_settings_*` - Cài đặt phòng ban

## API Endpoints Mới

### Department Management
```
POST   /api/administrator/departments          # Lấy danh sách phòng ban
GET    /api/administrator/departments/:id      # Lấy thông tin phòng ban
POST   /api/administrator/departments/create   # Tạo phòng ban mới
PUT    /api/administrator/departments/:id      # Cập nhật phòng ban
POST   /api/administrator/departments/assign-admin # Chỉ định admin
GET    /api/administrator/departments/:id/members  # Lấy danh sách thành viên
```

## Frontend Changes

### 1. Navigation Menu
- Thêm menu "Quản Lý Phòng Ban" cho System Admin
- Cập nhật menu "Thành Viên" hiển thị theo department permissions

### 2. User Management
- Hiển thị phòng ban thay vì role
- Hiển thị trạng thái Department Admin
- Form tạo/sửa user có chọn phòng ban

### 3. Department Management
- Trang danh sách phòng ban
- Form tạo/sửa phòng ban
- Quản lý thành viên phòng ban
- Chỉ định Department Admin

### 4. Permission Guards
- Cập nhật `PermissionGuard` hỗ trợ department permissions
- Cập nhật `usePermissions` hook
- Middleware phân quyền mới

## Backward Compatibility

Hệ thống vẫn hỗ trợ:
- Legacy `rule` field trong database
- Legacy permissions trong code
- Existing API endpoints

Điều này đảm bảo hệ thống hoạt động bình thường trong quá trình migration.

## Testing

### 1. Unit Tests
```bash
npm test -- --grep "department"
```

### 2. Integration Tests
```bash
npm run test:integration
```

### 3. Manual Testing
1. Đăng nhập với System Admin
2. Tạo phòng ban mới
3. Thêm thành viên vào phòng ban
4. Chỉ định Department Admin
5. Kiểm tra permissions

## Troubleshooting

### Lỗi Thường Gặp

1. **User không thể truy cập dashboard**
   - Kiểm tra user có department không
   - Kiểm tra permissions của user
   - Chạy migration script lại

2. **Department Admin không thể quản lý thành viên**
   - Kiểm tra `isDepartmentAdmin` flag
   - Kiểm tra user có trong department không
   - Kiểm tra department có admin assignment không

3. **Permission denied errors**
   - Kiểm tra middleware permissions
   - Kiểm tra frontend permission guards
   - Xem logs để debug

### Debug Commands

```bash
# Kiểm tra user permissions
db.users.findOne({email: "<EMAIL>"}, {permissions: 1, systemRole: 1, department: 1, isDepartmentAdmin: 1})

# Kiểm tra department
db.departments.find({}, {name: 1, code: 1, departmentAdmin: 1, defaultPermissions: 1})

# Kiểm tra users trong department
db.users.find({department: ObjectId("department_id")}, {email: 1, isDepartmentAdmin: 1})
```

## Rollback Plan

Nếu cần rollback:

1. Backup database trước khi migration
2. Restore từ backup
3. Revert code changes
4. Restart services

## Next Steps

1. Hoàn thiện UI/UX cho department management
2. Thêm advanced permissions
3. Implement department-specific features
4. Performance optimization
5. Remove legacy code sau khi stable
