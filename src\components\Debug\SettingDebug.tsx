"use client";

import { useSetting } from "@/context/SettingContext";

const SettingDebug = () => {
  const { setting, loading, refreshSettings } = useSetting();

  if (loading) {
    return <div className="p-4 bg-yellow-100 rounded">Loading settings...</div>;
  }

  return (
    <div className="p-4 bg-gray-100 rounded mb-4">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">Setting Debug Info:</h3>
        <button
          onClick={refreshSettings}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          🔄 Refresh Settings
        </button>
      </div>
      <div className="mb-2">
        <strong>Logo Path:</strong> {setting?.logo?.path || 'No logo'}
      </div>
      <pre className="text-xs overflow-auto max-h-40">
        {JSON.stringify(setting, null, 2)}
      </pre>
    </div>
  );
};

export default SettingDebug;
