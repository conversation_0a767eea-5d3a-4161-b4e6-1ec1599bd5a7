const express = require("express");
const router = express.Router();
const adminController = require("../controllers/admin");
const passport = require('passport')
const {verifyAdmin, verifyManager}  = require('../middleware/is-admin')
const checkBlacklistedToken = require("../middleware/checkBlacklistedToken");
const {
  mongoIdValidation
} = require("../middleware/inputValidation");
const {
  verifyDashboardAccess,
  verifySystemSettingsView
} = require("../middleware/permission-check");



const { dashboardLimiter } = require("../middleware/security");

router.get("/recent-activities", dashboardLimiter, passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyDashboardAccess, adminController.getRecentActivities);





module.exports = router;
