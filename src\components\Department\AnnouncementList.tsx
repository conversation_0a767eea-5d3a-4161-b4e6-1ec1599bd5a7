"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import Link from "next/link";
import {
  Volume2, // Thay thế Megaphone
  Eye,
  Edit,
  Trash2,
  Plus,
  Filter,
  Calendar,
  User,
  AlertTriangle,
  Clock,
  Heart,
  MessageCircle,
  CheckCircle
} from "react-feather";
import departmentAnnouncementApiRequest, { DepartmentAnnouncement } from "@/apiRequests/departmentAnnouncement";

interface AnnouncementListProps {
  departmentId: string;
  departmentName: string;
}

export default function AnnouncementList({ departmentId, departmentName }: AnnouncementListProps) {
  const [announcements, setAnnouncements] = useState<DepartmentAnnouncement[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: "",
    type: "",
    priority: "",
    activeOnly: true
  });
  const perPage = 10;

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentAnnouncementApiRequest.getDepartmentAnnouncements(
        departmentId,
        { 
          page, 
          perPage,
          ...filters
        },
        sessionToken
      );
      
      if (response && response.payload && response.payload.success) {
        setAnnouncements(response.payload.announcements || []);
        setTotalPages(response.payload.totalPages || 1);
      } else {
        toast.error("Không thể tải danh sách thông báo");
      }
    } catch (error: any) {
      console.error("Error fetching announcements:", error);
      toast.error("Có lỗi xảy ra khi tải thông báo");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAnnouncement = async (announcementId: string, title: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa thông báo "${title}"?`)) {
      return;
    }

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentAnnouncementApiRequest.deleteAnnouncement(announcementId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        toast.success("Xóa thông báo thành công");
        fetchAnnouncements();
      } else {
        toast.error("Không thể xóa thông báo");
      }
    } catch (error: any) {
      console.error("Error deleting announcement:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi xóa thông báo";
      toast.error(errorMessage);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'urgent':
        return <AlertTriangle size={16} className="text-red-500" />;
      case 'meeting':
        return <Calendar size={16} className="text-blue-500" />;
      case 'deadline':
        return <Clock size={16} className="text-orange-500" />;
      case 'policy':
        return <CheckCircle size={16} className="text-purple-500" />;
      case 'event':
        return <Calendar size={16} className="text-green-500" />;
      case 'training':
        return <User size={16} className="text-indigo-500" />;
      default:
        return <Volume2 size={16} className="text-gray-500" />;
    }
  };

  const getTypeName = (type: string) => {
    const typeNames = {
      general: 'Thông báo chung',
      urgent: 'Khẩn cấp',
      meeting: 'Họp',
      deadline: 'Hạn chót',
      policy: 'Chính sách',
      event: 'Sự kiện',
      training: 'Đào tạo',
      maintenance: 'Bảo trì'
    };
    return typeNames[type as keyof typeof typeNames] || type;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { color: 'bg-gray-100 text-gray-800', text: 'Thấp' },
      normal: { color: 'bg-blue-100 text-blue-800', text: 'Bình thường' },
      high: { color: 'bg-yellow-100 text-yellow-800', text: 'Cao' },
      urgent: { color: 'bg-red-100 text-red-800', text: 'Khẩn cấp' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.normal;
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', text: 'Nháp' },
      published: { color: 'bg-green-100 text-green-800', text: 'Đã xuất bản' },
      archived: { color: 'bg-yellow-100 text-yellow-800', text: 'Lưu trữ' },
      expired: { color: 'bg-red-100 text-red-800', text: 'Hết hạn' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.published;
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.color}`}>
        {config.text}
      </span>
    );
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [page, filters]);

  const handleFilterChange = (key: string, value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1); // Reset to first page when filtering
  };

  if (loading) {
    return (
      <div className="w-full p-4 flex items-center justify-center">
        <div className="text-gray-600">Đang tải thông báo...</div>
      </div>
    );
  }

  return (
    <div className="w-full p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Thông báo phòng ban</h2>
          <p className="text-gray-600">{departmentName}</p>
        </div>
        <Link
          href={`/dashboard/departments/${departmentId}/announcements/create`}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={16} className="mr-2" />
          Tạo thông báo
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex items-center space-x-4">
          <Filter size={16} className="text-gray-500" />
          <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="published">Đã xuất bản</option>
              <option value="draft">Nháp</option>
              <option value="archived">Lưu trữ</option>
              <option value="expired">Hết hạn</option>
            </select>

            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả loại</option>
              <option value="general">Thông báo chung</option>
              <option value="urgent">Khẩn cấp</option>
              <option value="meeting">Họp</option>
              <option value="deadline">Hạn chót</option>
              <option value="policy">Chính sách</option>
              <option value="event">Sự kiện</option>
              <option value="training">Đào tạo</option>
              <option value="maintenance">Bảo trì</option>
            </select>

            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả mức độ</option>
              <option value="low">Thấp</option>
              <option value="normal">Bình thường</option>
              <option value="high">Cao</option>
              <option value="urgent">Khẩn cấp</option>
            </select>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={filters.activeOnly}
                onChange={(e) => handleFilterChange('activeOnly', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600">Chỉ hiển thị đang hoạt động</span>
            </label>
          </div>
        </div>
      </div>

      {/* Announcements List */}
      {announcements.length === 0 ? (
        <div className="text-center text-gray-600 py-8">
          <div className="mb-4">
            <Volume2 size={48} className="mx-auto text-gray-300" />
          </div>
          <p className="text-lg mb-2">Chưa có thông báo nào</p>
          <p className="text-sm text-gray-500 mb-4">
            Tạo thông báo đầu tiên để giao tiếp với thành viên phòng ban
          </p>
          <Link
            href={`/dashboard/departments/${departmentId}/announcements/create`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Tạo thông báo
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="divide-y divide-gray-200">
            {announcements.map((announcement) => (
              <div key={announcement._id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getTypeIcon(announcement.type)}
                      <h3 className="text-lg font-medium text-gray-900">{announcement.title}</h3>
                      {getPriorityBadge(announcement.priority)}
                      {getStatusBadge(announcement.status)}
                      {announcement.requireAcknowledgment && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                          Cần xác nhận
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{announcement.summary || announcement.content.substring(0, 200) + '...'}</p>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar size={14} />
                        <span>
                          {announcement.publishedAt 
                            ? new Date(announcement.publishedAt).toLocaleDateString('vi-VN')
                            : 'Chưa xuất bản'
                          }
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <User size={14} />
                        <span>{announcement.author.username}</span>
                      </div>
                      <span>{getTypeName(announcement.type)}</span>
                      {announcement.expiresAt && (
                        <div className="flex items-center space-x-1">
                          <Clock size={14} />
                          <span>Hết hạn: {new Date(announcement.expiresAt).toLocaleDateString('vi-VN')}</span>
                        </div>
                      )}
                    </div>

                    {/* Analytics */}
                    <div className="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Eye size={14} />
                        <span>{announcement.analytics.totalViews} lượt xem</span>
                      </div>
                      {announcement.allowLikes && (
                        <div className="flex items-center space-x-1">
                          <Heart size={14} />
                          <span>{announcement.analytics.totalLikes} lượt thích</span>
                        </div>
                      )}
                      {announcement.allowComments && (
                        <div className="flex items-center space-x-1">
                          <MessageCircle size={14} />
                          <span>{announcement.analytics.totalComments} bình luận</span>
                        </div>
                      )}
                      {announcement.requireAcknowledgment && (
                        <div className="flex items-center space-x-1">
                          <CheckCircle size={14} />
                          <span>{announcement.analytics.acknowledgmentRate.toFixed(1)}% đã xác nhận</span>
                        </div>
                      )}
                    </div>

                    {announcement.tags && announcement.tags.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {announcement.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Link
                      href={`/dashboard/departments/${departmentId}/announcements/${announcement._id}`}
                      className="text-blue-600 hover:text-blue-800 p-2 rounded hover:bg-blue-50"
                      title="Xem thông báo"
                    >
                      <Eye size={16} />
                    </Link>
                    <Link
                      href={`/dashboard/departments/${departmentId}/announcements/${announcement._id}/edit`}
                      className="text-green-600 hover:text-green-800 p-2 rounded hover:bg-green-50"
                      title="Chỉnh sửa"
                    >
                      <Edit size={16} />
                    </Link>
                    <button
                      onClick={() => handleDeleteAnnouncement(announcement._id, announcement.title)}
                      className="text-red-600 hover:text-red-800 p-2 rounded hover:bg-red-50"
                      title="Xóa thông báo"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Trang {page} / {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Trước
                </button>
                <button
                  onClick={() => setPage(Math.min(totalPages, page + 1))}
                  disabled={page === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Sau
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
