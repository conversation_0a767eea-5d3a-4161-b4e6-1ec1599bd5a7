const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { securityLogger } = require('./securityLogger');

/**
 * Centralized security configuration middleware
 */

// Content Security Policy configuration
const cspConfig = {
  directives: {
    defaultSrc: ["'self'"],
    styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
    fontSrc: ["'self'", "https://fonts.gstatic.com"],
    imgSrc: ["'self'", "data:", "https:"],
    scriptSrc: ["'self'"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"],
    connectSrc: ["'self'"],
    workerSrc: ["'none'"],
    manifestSrc: ["'self'"],
    upgradeInsecureRequests: []
  }
};

// Rate limiting configurations for different endpoints
const rateLimitConfigs = {
  // General API rate limiting
  general: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // limit each IP to 1000 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      securityLogger.logRateLimit(req, 1000, 15 * 60 * 1000);
      res.status(429).json({
        error: 'Too many requests from this IP, please try again later.'
      });
    }
  }),

  // Authentication endpoints (more lenient for development)
  auth: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 login attempts per windowMs (increased for development)
    message: {
      error: 'Too many authentication attempts from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true, // Don't count successful requests
    handler: (req, res) => {
      securityLogger.logRateLimit(req, 100, 15 * 60 * 1000);
      res.status(429).json({
        error: 'Too many authentication attempts from this IP, please try again later.'
      });
    }
  }),

  // File upload endpoints
  upload: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // limit each IP to 200 uploads per windowMs (increased from 50)
    message: {
      error: 'Too many file uploads from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      securityLogger.logRateLimit(req, 200, 15 * 60 * 1000);
      res.status(429).json({
        error: 'Too many file uploads from this IP, please try again later.'
      });
    }
  }),

  // Video upload endpoints (more lenient for large files)
  video: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 video uploads per windowMs
    message: {
      error: 'Too many video uploads from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      securityLogger.logRateLimit(req, 100, 15 * 60 * 1000);
      res.status(429).json({
        error: 'Too many video uploads from this IP, please try again later.'
      });
    }
  }),
  // Admin operations (very restrictive)
  admin: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // limit each IP to 200 admin operations per windowMs (increased for development)
    message: {
      error: 'Too many admin operations from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,    handler: (req, res) => {
      securityLogger.logRateLimit(req, 200, 15 * 60 * 1000);
      res.status(429).json({
        error: 'Too many admin operations from this IP, please try again later.'
      });
    }
  })
};

// Security headers middleware
const securityHeaders = helmet({
  contentSecurityPolicy: cspConfig,
  crossOriginEmbedderPolicy: false, // Disable for compatibility
  crossOriginResourcePolicy: { policy: "cross-origin" }, // Allow cross-origin resource sharing
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'same-origin' }
});

// IP whitelist for admin operations (if needed)
const adminIpWhitelist = process.env.ADMIN_IP_WHITELIST ? 
  process.env.ADMIN_IP_WHITELIST.split(',').map(ip => ip.trim()) : [];

// IP whitelist middleware for admin routes
const adminIpRestriction = (req, res, next) => {
  if (adminIpWhitelist.length === 0) {
    return next(); // No IP restriction if whitelist is empty
  }

  const clientIp = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  const forwardedIps = req.get('X-Forwarded-For');
  
  let allowedIp = adminIpWhitelist.includes(clientIp);
  
  if (!allowedIp && forwardedIps) {
    const ips = forwardedIps.split(',').map(ip => ip.trim());
    allowedIp = ips.some(ip => adminIpWhitelist.includes(ip));
  }
  
  if (!allowedIp) {
    securityLogger.logUnauthorizedAccess(req, 'IP not in admin whitelist');
    return res.status(403).json({
      error: 'Access denied from this IP address'
    });
  }
  
  next();
};

// Request sanitization middleware
const sanitizeRequest = (req, res, next) => {
  // Remove potentially dangerous properties from request body
  if (req.body && typeof req.body === 'object') {
    delete req.body.__proto__;
    delete req.body.constructor;
    delete req.body.prototype;
  }
  
  // Remove null bytes from URL parameters
  if (req.params) {
    for (const key in req.params) {
      if (typeof req.params[key] === 'string') {
        req.params[key] = req.params[key].replace(/\0/g, '');
      }
    }
  }
  
  // Remove null bytes from query parameters
  if (req.query) {
    for (const key in req.query) {
      if (typeof req.query[key] === 'string') {
        req.query[key] = req.query[key].replace(/\0/g, '');
      }
    }
  }
  
  next();
};

// Security monitoring middleware
const securityMonitoring = (req, res, next) => {
  // Monitor for suspicious patterns in requests
  const suspiciousPatterns = [
    /\.\.\//,     // Path traversal
    /\0/,         // Null bytes
    /<script/i,   // XSS
    /javascript:/i, // JavaScript protocol
    /data:.*base64/i, // Base64 data URLs
    /eval\s*\(/i, // Code injection
    /exec\s*\(/i, // Command injection
    /system\s*\(/i // System calls
  ];
  
  const requestString = JSON.stringify({
    url: req.url,
    body: req.body,
    query: req.query,
    params: req.params
  });
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestString)) {
      securityLogger.logSuspiciousActivity(req, 'SUSPICIOUS_PATTERN_DETECTED', {
        pattern: pattern.toString(),
        url: req.url
      });
      break;
    }
  }
  
  next();
};

module.exports = {
  securityHeaders,
  rateLimitConfigs,
  adminIpRestriction,
  sanitizeRequest,
  securityMonitoring,
  cspConfig
};
