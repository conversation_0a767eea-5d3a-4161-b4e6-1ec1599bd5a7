const mongoose = require("mongoose");
const { Schema } = mongoose;
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const departmentFileSchema = new Schema(
  {
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true
    },
    fileName: {
      type: String,
      required: true,
      trim: true
    },
    originalName: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    filePath: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    mimeType: {
      type: String,
      required: true
    },
    fileExtension: {
      type: String,
      required: true
    },
    
    // File categorization
    category: {
      type: String,
      enum: [
        "document",     // Tài liệu
        "image",        // Hình ảnh
        "video",        // Video
        "audio",        // Âm thanh
        "archive",      // File nén
        "spreadsheet",  // Bảng tính
        "presentation", // Thuyết trình
        "other"         // Khác
      ],
      default: "document"
    },
    tags: [{
      type: String,
      trim: true
    }],
    
    // Upload information
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    
    // Version control
    version: {
      type: String,
      default: "1.0"
    },
    parentFile: {
      type: Schema.Types.ObjectId,
      ref: "DepartmentFile"
    },
    isLatestVersion: {
      type: Boolean,
      default: true
    },
    versionHistory: [{
      version: String,
      uploadedBy: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      uploadedAt: {
        type: Date,
        default: Date.now
      },
      changes: String,
      filePath: String,
      fileSize: Number
    }],
    
    // Access control
    visibility: {
      type: String,
      enum: ["public", "department", "restricted", "private"],
      default: "department"
    },
    accessPermissions: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      permissions: [{
        type: String,
        enum: ["view", "download", "edit", "delete", "share"]
      }],
      grantedBy: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      grantedAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    // File status
    status: {
      type: String,
      enum: ["active", "archived", "deleted", "quarantined"],
      default: "active"
    },
    
    // Security and scanning
    isScanned: {
      type: Boolean,
      default: false
    },
    scanResult: {
      type: String,
      enum: ["clean", "infected", "suspicious", "pending"],
      default: "pending"
    },
    scanDate: {
      type: Date
    },
    
    // File metadata
    metadata: {
      // Image metadata
      dimensions: {
        width: Number,
        height: Number
      },
      // Video/Audio metadata
      duration: Number,
      bitrate: Number,
      // Document metadata
      pageCount: Number,
      wordCount: Number,
      // General metadata
      encoding: String,
      compression: String,
      checksum: String
    },
    
    // Access tracking
    downloads: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      downloadedAt: {
        type: Date,
        default: Date.now
      },
      ipAddress: String,
      userAgent: String
    }],
    
    views: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      viewedAt: {
        type: Date,
        default: Date.now
      },
      ipAddress: String,
      userAgent: String
    }],
    
    // Sharing
    shares: [{
      sharedWith: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      sharedBy: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      sharedAt: {
        type: Date,
        default: Date.now
      },
      permissions: [{
        type: String,
        enum: ["view", "download", "edit"]
      }],
      expiresAt: {
        type: Date
      },
      accessCount: {
        type: Number,
        default: 0
      }
    }],
    
    // Comments and collaboration
    comments: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true
      },
      content: {
        type: String,
        required: true,
        trim: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      },
      editedAt: {
        type: Date
      },
      isEdited: {
        type: Boolean,
        default: false
      }
    }],
    
    // File organization
    folder: {
      type: String,
      default: "/"
    },
    isFavorite: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      addedAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    // Analytics
    analytics: {
      totalDownloads: {
        type: Number,
        default: 0
      },
      totalViews: {
        type: Number,
        default: 0
      },
      uniqueDownloads: {
        type: Number,
        default: 0
      },
      uniqueViews: {
        type: Number,
        default: 0
      },
      lastAccessed: {
        type: Date
      }
    },
    
    // Expiration and cleanup
    expiresAt: {
      type: Date
    },
    autoDelete: {
      type: Boolean,
      default: false
    },
    
    // Backup and sync
    isBackedUp: {
      type: Boolean,
      default: false
    },
    backupPath: {
      type: String
    },
    lastBackup: {
      type: Date
    }
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better performance
departmentFileSchema.index({ department: 1, status: 1 });
departmentFileSchema.index({ uploadedBy: 1 });
departmentFileSchema.index({ fileName: "text", originalName: "text", description: "text" });
departmentFileSchema.index({ category: 1 });
departmentFileSchema.index({ tags: 1 });
departmentFileSchema.index({ mimeType: 1 });
departmentFileSchema.index({ uploadedAt: -1 });
departmentFileSchema.index({ folder: 1 });
departmentFileSchema.index({ visibility: 1 });
departmentFileSchema.index({ expiresAt: 1 });

// Plugin for CASL permissions
departmentFileSchema.plugin(accessibleRecordsPlugin);

// Virtual for file size in human readable format
departmentFileSchema.virtual('fileSizeFormatted').get(function() {
  const bytes = this.fileSize;
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Virtual for checking if file is expired
departmentFileSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt <= new Date();
});

// Virtual for file URL (would need to be configured based on your file serving setup)
departmentFileSchema.virtual('fileUrl').get(function() {
  return `/api/files/department/${this.department}/${this.fileName}`;
});

// Static methods
departmentFileSchema.statics.findByDepartment = function(departmentId, options = {}) {
  const query = { 
    department: departmentId,
    status: options.status || 'active'
  };
  
  if (options.category) {
    query.category = options.category;
  }
  
  if (options.folder) {
    query.folder = options.folder;
  }
  
  if (options.tags && options.tags.length > 0) {
    query.tags = { $in: options.tags };
  }
  
  if (options.uploadedBy) {
    query.uploadedBy = options.uploadedBy;
  }
  
  if (options.search) {
    query.$text = { $search: options.search };
  }
  
  return this.find(query)
    .populate('uploadedBy', 'username email')
    .populate('department', 'name code')
    .sort(options.sort || { uploadedAt: -1 });
};

departmentFileSchema.statics.findAccessibleFiles = function(userId, departmentId) {
  return this.find({
    department: departmentId,
    status: 'active',
    $or: [
      { visibility: 'public' },
      { visibility: 'department' },
      { uploadedBy: userId },
      { 'accessPermissions.user': userId }
    ]
  })
  .populate('uploadedBy', 'username email')
  .sort({ uploadedAt: -1 });
};

// Instance methods
departmentFileSchema.methods.addDownload = function(userId, ipAddress, userAgent) {
  // Check if user already downloaded this file
  const existingDownload = this.downloads.find(download => 
    download.user && download.user.toString() === userId.toString()
  );
  
  if (!existingDownload) {
    this.analytics.uniqueDownloads += 1;
  }
  
  this.downloads.push({
    user: userId,
    ipAddress,
    userAgent
  });
  
  this.analytics.totalDownloads += 1;
  this.analytics.lastAccessed = new Date();
  
  return this.save();
};

departmentFileSchema.methods.addView = function(userId, ipAddress, userAgent) {
  // Check if user already viewed this file
  const existingView = this.views.find(view => 
    view.user && view.user.toString() === userId.toString()
  );
  
  if (!existingView) {
    this.analytics.uniqueViews += 1;
  }
  
  this.views.push({
    user: userId,
    ipAddress,
    userAgent
  });
  
  this.analytics.totalViews += 1;
  this.analytics.lastAccessed = new Date();
  
  return this.save();
};

departmentFileSchema.methods.shareWith = function(userId, sharedBy, permissions = ['view'], expiresAt = null) {
  // Remove existing share if exists
  this.shares = this.shares.filter(
    share => !share.sharedWith.equals(userId)
  );
  
  // Add new share
  this.shares.push({
    sharedWith: userId,
    sharedBy: sharedBy,
    permissions: permissions,
    expiresAt: expiresAt
  });
  
  return this.save();
};

departmentFileSchema.methods.addToFavorites = function(userId) {
  // Check if already in favorites
  const existingFavorite = this.isFavorite.find(fav => 
    fav.user.toString() === userId.toString()
  );
  
  if (existingFavorite) {
    return { success: false, message: "Already in favorites" };
  }
  
  this.isFavorite.push({ user: userId });
  return this.save().then(() => ({ success: true, message: "Added to favorites" }));
};

departmentFileSchema.methods.removeFromFavorites = function(userId) {
  const favoriteIndex = this.isFavorite.findIndex(fav => 
    fav.user.toString() === userId.toString()
  );
  
  if (favoriteIndex === -1) {
    return { success: false, message: "Not in favorites" };
  }
  
  this.isFavorite.splice(favoriteIndex, 1);
  return this.save().then(() => ({ success: true, message: "Removed from favorites" }));
};

departmentFileSchema.methods.canAccess = function(user, permission = 'view') {
  // File owner can do anything
  if (this.uploadedBy.equals(user._id)) {
    return true;
  }
  
  // Department admin can access department files
  if (user.isDepartmentAdmin && user.department.equals(this.department)) {
    return true;
  }
  
  // System admin can access everything
  if (user.systemRole === 'admin') {
    return true;
  }
  
  // Check visibility
  if (this.visibility === 'public') {
    return true;
  }
  
  if (this.visibility === 'department' && user.department && user.department.equals(this.department)) {
    return true;
  }
  
  // Check specific permissions
  const accessPermission = this.accessPermissions.find(
    perm => perm.user.equals(user._id)
  );
  
  if (accessPermission && accessPermission.permissions.includes(permission)) {
    return true;
  }
  
  // Check shares
  const share = this.shares.find(
    share => share.sharedWith.equals(user._id) && 
             (!share.expiresAt || share.expiresAt > new Date())
  );
  
  if (share && share.permissions.includes(permission)) {
    return true;
  }
  
  return false;
};

// Pre-save middleware
departmentFileSchema.pre('save', function(next) {
  // Auto-detect category based on mime type
  if (!this.category || this.category === 'other') {
    if (this.mimeType.startsWith('image/')) {
      this.category = 'image';
    } else if (this.mimeType.startsWith('video/')) {
      this.category = 'video';
    } else if (this.mimeType.startsWith('audio/')) {
      this.category = 'audio';
    } else if (this.mimeType.includes('spreadsheet') || this.mimeType.includes('excel')) {
      this.category = 'spreadsheet';
    } else if (this.mimeType.includes('presentation') || this.mimeType.includes('powerpoint')) {
      this.category = 'presentation';
    } else if (this.mimeType.includes('zip') || this.mimeType.includes('rar') || this.mimeType.includes('archive')) {
      this.category = 'archive';
    } else if (this.mimeType.includes('document') || this.mimeType.includes('pdf') || this.mimeType.includes('text')) {
      this.category = 'document';
    }
  }
  
  // Extract file extension
  if (!this.fileExtension) {
    const lastDot = this.originalName.lastIndexOf('.');
    if (lastDot > 0) {
      this.fileExtension = this.originalName.substring(lastDot + 1).toLowerCase();
    }
  }
  
  // Normalize folder path
  if (this.folder && !this.folder.startsWith('/')) {
    this.folder = '/' + this.folder;
  }
  if (this.folder && !this.folder.endsWith('/') && this.folder !== '/') {
    this.folder = this.folder + '/';
  }
  
  next();
});

module.exports = mongoose.model("DepartmentFile", departmentFileSchema);
