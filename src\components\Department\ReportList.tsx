"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import Link from "next/link";
import { 
  FileText, 
  Download, 
  Eye, 
  Edit, 
  Trash2, 
  Plus, 
  Filter,
  Calendar,
  User,
  BarChart3,
  Activity
} from "react-feather";
import departmentReportApiRequest, { DepartmentReport } from "@/apiRequests/departmentReport";

interface ReportListProps {
  departmentId: string;
  departmentName: string;
}

export default function ReportList({ departmentId, departmentName }: ReportListProps) {
  const [reports, setReports] = useState<DepartmentReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    reportType: "",
    status: "",
    startDate: "",
    endDate: ""
  });
  const perPage = 10;

  const fetchReports = async () => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentReportApiRequest.getDepartmentReports(
        departmentId,
        { 
          page, 
          perPage,
          ...filters
        },
        sessionToken
      );
      
      if (response && response.payload && response.payload.success) {
        setReports(response.payload.reports || []);
        setTotalPages(response.payload.totalPages || 1);
      } else {
        toast.error("Không thể tải danh sách báo cáo");
      }
    } catch (error: any) {
      console.error("Error fetching reports:", error);
      toast.error("Có lỗi xảy ra khi tải báo cáo");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteReport = async (reportId: string, reportTitle: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa báo cáo "${reportTitle}"?`)) {
      return;
    }

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentReportApiRequest.deleteReport(reportId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        toast.success("Xóa báo cáo thành công");
        fetchReports();
      } else {
        toast.error("Không thể xóa báo cáo");
      }
    } catch (error: any) {
      console.error("Error deleting report:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi xóa báo cáo";
      toast.error(errorMessage);
    }
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case 'monthly_summary':
        return <BarChart3 size={16} className="text-blue-500" />;
      case 'member_activity':
        return <Activity size={16} className="text-green-500" />;
      case 'performance_metrics':
        return <BarChart3 size={16} className="text-purple-500" />;
      case 'file_usage':
        return <FileText size={16} className="text-orange-500" />;
      default:
        return <FileText size={16} className="text-gray-500" />;
    }
  };

  const getReportTypeName = (type: string) => {
    switch (type) {
      case 'monthly_summary':
        return 'Tổng kết tháng';
      case 'member_activity':
        return 'Hoạt động thành viên';
      case 'performance_metrics':
        return 'Chỉ số hiệu suất';
      case 'file_usage':
        return 'Sử dụng file';
      case 'custom_report':
        return 'Báo cáo tùy chỉnh';
      default:
        return type;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      generating: { color: 'bg-yellow-100 text-yellow-800', text: 'Đang tạo' },
      completed: { color: 'bg-green-100 text-green-800', text: 'Hoàn thành' },
      failed: { color: 'bg-red-100 text-red-800', text: 'Thất bại' },
      archived: { color: 'bg-gray-100 text-gray-800', text: 'Lưu trữ' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.completed;
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.color}`}>
        {config.text}
      </span>
    );
  };

  useEffect(() => {
    fetchReports();
  }, [page, filters]);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1); // Reset to first page when filtering
  };

  if (loading) {
    return (
      <div className="w-full p-4 flex items-center justify-center">
        <div className="text-gray-600">Đang tải báo cáo...</div>
      </div>
    );
  }

  return (
    <div className="w-full p-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Báo cáo phòng ban</h2>
          <p className="text-gray-600">{departmentName}</p>
        </div>
        <Link
          href={`/dashboard/departments/${departmentId}/reports/create`}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={16} className="mr-2" />
          Tạo báo cáo
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex items-center space-x-4">
          <Filter size={16} className="text-gray-500" />
          <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
            <select
              value={filters.reportType}
              onChange={(e) => handleFilterChange('reportType', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả loại báo cáo</option>
              <option value="monthly_summary">Tổng kết tháng</option>
              <option value="member_activity">Hoạt động thành viên</option>
              <option value="performance_metrics">Chỉ số hiệu suất</option>
              <option value="file_usage">Sử dụng file</option>
              <option value="custom_report">Báo cáo tùy chỉnh</option>
            </select>

            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="completed">Hoàn thành</option>
              <option value="generating">Đang tạo</option>
              <option value="failed">Thất bại</option>
              <option value="archived">Lưu trữ</option>
            </select>

            <input
              type="date"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Từ ngày"
            />

            <input
              type="date"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Đến ngày"
            />
          </div>
        </div>
      </div>

      {/* Reports List */}
      {reports.length === 0 ? (
        <div className="text-center text-gray-600 py-8">
          <div className="mb-4">
            <FileText size={48} className="mx-auto text-gray-300" />
          </div>
          <p className="text-lg mb-2">Chưa có báo cáo nào</p>
          <p className="text-sm text-gray-500 mb-4">
            Tạo báo cáo đầu tiên để theo dõi hoạt động phòng ban
          </p>
          <Link
            href={`/dashboard/departments/${departmentId}/reports/create`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Tạo báo cáo
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="divide-y divide-gray-200">
            {reports.map((report) => (
              <div key={report._id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getReportTypeIcon(report.reportType)}
                      <h3 className="text-lg font-medium text-gray-900">{report.title}</h3>
                      {getStatusBadge(report.status)}
                      {report.isPublic && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          Công khai
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-600 mb-3">{report.description || "Không có mô tả"}</p>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar size={14} />
                        <span>
                          {new Date(report.reportPeriod.startDate).toLocaleDateString('vi-VN')} - {' '}
                          {new Date(report.reportPeriod.endDate).toLocaleDateString('vi-VN')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <User size={14} />
                        <span>{report.generatedBy.username}</span>
                      </div>
                      <span>{getReportTypeName(report.reportType)}</span>
                    </div>

                    {report.tags && report.tags.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {report.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <Link
                      href={`/dashboard/departments/${departmentId}/reports/${report._id}`}
                      className="text-blue-600 hover:text-blue-800 p-2 rounded hover:bg-blue-50"
                      title="Xem báo cáo"
                    >
                      <Eye size={16} />
                    </Link>
                    <Link
                      href={`/dashboard/departments/${departmentId}/reports/${report._id}/edit`}
                      className="text-green-600 hover:text-green-800 p-2 rounded hover:bg-green-50"
                      title="Chỉnh sửa"
                    >
                      <Edit size={16} />
                    </Link>
                    <button
                      className="text-gray-600 hover:text-gray-800 p-2 rounded hover:bg-gray-50"
                      title="Tải xuống"
                    >
                      <Download size={16} />
                    </button>
                    <button
                      onClick={() => handleDeleteReport(report._id, report.title)}
                      className="text-red-600 hover:text-red-800 p-2 rounded hover:bg-red-50"
                      title="Xóa báo cáo"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Trang {page} / {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Trước
                </button>
                <button
                  onClick={() => setPage(Math.min(totalPages, page + 1))}
                  disabled={page === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Sau
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
