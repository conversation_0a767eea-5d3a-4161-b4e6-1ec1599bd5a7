"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import fileApiRequest from "@/apiRequests/file";
import { formatBytes, formatDate } from "@/utils/formatters";

interface SyncStatusProps {
  onSyncComplete?: () => void;
}

const SyncStatus: React.FC<SyncStatusProps> = ({ onSyncComplete }) => {
  const [syncStatus, setSyncStatus] = useState<any>(null);
  const [syncLoading, setSyncLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Fetch sync status
  const fetchSyncStatus = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await fileApiRequest.getSyncStatus(sessionToken);
      
      if (result.payload.success) {
        setSyncStatus(result.payload.status);
      }
    } catch (error) {
      console.error("Error fetching sync status:", error);
    }
  };

  // Handle sync existing files
  const handleSyncExisting = async () => {
    if (!syncStatus || syncStatus.missingInDatabase === 0) {
      toast.info("Không có file nào cần đồng bộ");
      return;
    }

    if (!confirm(`Bạn có chắc muốn đồng bộ ${syncStatus.missingInDatabase} files? Quá trình này có thể mất một ít thời gian.`)) {
      return;
    }

    try {
      setSyncLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await fileApiRequest.syncExistingFiles(sessionToken);
      
      if (result.payload.success) {
        toast.success("Đã bắt đầu đồng bộ file. Vui lòng kiểm tra lại sau vài phút.");
        
        // Refresh sync status after a delay
        setTimeout(() => {
          fetchSyncStatus();
          onSyncComplete?.();
        }, 3000);
      } else {
        toast.error("Không thể bắt đầu đồng bộ file");
      }
    } catch (error) {
      console.error("Error syncing files:", error);
      toast.error("Đã xảy ra lỗi khi đồng bộ file");
    } finally {
      setSyncLoading(false);
    }
  };

  useEffect(() => {
    fetchSyncStatus();
    
    // Auto refresh every 30 seconds
    const interval = setInterval(fetchSyncStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  if (!syncStatus) {
    return (
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
          <div className="h-6 bg-gray-300 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const isSynced = syncStatus.missingInDatabase === 0;

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="text-2xl mr-3">
            {syncLoading ? "🔄" : isSynced ? "✅" : "⚠️"}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">File Sync Status</h3>
            <p className="text-sm text-gray-600">
              Trạng thái đồng bộ giữa thư mục vật lý và database
            </p>
          </div>
        </div>
        
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          {showDetails ? "Ẩn chi tiết" : "Xem chi tiết"}
        </button>
      </div>

      {/* Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 rounded">
          <div className="text-2xl font-bold text-blue-600">
            {syncStatus.totalPhysicalFiles}
          </div>
          <div className="text-xs text-gray-600">Files vật lý</div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 rounded">
          <div className="text-2xl font-bold text-green-600">
            {syncStatus.totalDatabaseFiles}
          </div>
          <div className="text-xs text-gray-600">Files trong DB</div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 rounded">
          <div className={`text-2xl font-bold ${syncStatus.missingInDatabase > 0 ? 'text-orange-600' : 'text-green-600'}`}>
            {syncStatus.missingInDatabase}
          </div>
          <div className="text-xs text-gray-600">Chưa đồng bộ</div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 rounded">
          <div className={`text-2xl font-bold ${isSynced ? 'text-green-600' : 'text-orange-600'}`}>
            {isSynced ? '100%' : Math.round((syncStatus.totalDatabaseFiles / syncStatus.totalPhysicalFiles) * 100) + '%'}
          </div>
          <div className="text-xs text-gray-600">Tỉ lệ sync</div>
        </div>
      </div>

      {/* Status Message */}
      <div className={`p-3 rounded-md mb-4 ${isSynced ? 'bg-green-50 text-green-800' : 'bg-orange-50 text-orange-800'}`}>
        {syncLoading ? (
          <span className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
            Đang đồng bộ files...
          </span>
        ) : isSynced ? (
          "✅ Tất cả files đã được đồng bộ"
        ) : (
          `⚠️ Có ${syncStatus.missingInDatabase} files chưa được đồng bộ vào database`
        )}
      </div>

      {/* Action Button */}
      {!isSynced && !syncLoading && (
        <button
          onClick={handleSyncExisting}
          disabled={syncLoading}
          className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50"
        >
          🔄 Đồng bộ {syncStatus.missingInDatabase} files
        </button>
      )}

      {/* Details */}
      {showDetails && (
        <div className="mt-4 border-t pt-4">
          <h4 className="font-semibold text-gray-900 mb-2">Files chưa đồng bộ (10 đầu tiên):</h4>
          {syncStatus.missingFiles && syncStatus.missingFiles.length > 0 ? (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {syncStatus.missingFiles.map((file: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                  <div className="flex-1 min-w-0">
                    <div className="truncate font-medium text-gray-900">
                      {file.filename}
                    </div>
                    <div className="text-gray-500 text-xs">
                      {file.relativePath}
                    </div>
                  </div>
                  <div className="ml-4 text-gray-600 text-xs">
                    {formatBytes(file.size)}
                  </div>
                </div>
              ))}
              {syncStatus.missingInDatabase > syncStatus.missingFiles.length && (
                <div className="text-center text-gray-500 text-sm py-2">
                  ... và {syncStatus.missingInDatabase - syncStatus.missingFiles.length} files khác
                </div>
              )}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">Không có files nào cần đồng bộ</div>
          )}
        </div>
      )}

      {/* Refresh Button */}
      <div className="mt-4 flex justify-between items-center text-sm text-gray-500">
        <span>Cập nhật lần cuối: {new Date().toLocaleTimeString()}</span>
        <button
          onClick={fetchSyncStatus}
          className="text-blue-600 hover:text-blue-800"
        >
          🔄 Làm mới
        </button>
      </div>
    </div>
  );
};

export default SyncStatus;
