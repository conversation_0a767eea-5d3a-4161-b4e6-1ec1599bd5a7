const validator = require('validator');
const xss = require('xss');

/**
 * Advanced input validation and sanitization middleware
 */

// Common validation rules
const validationRules = {
  email: {
    required: true,
    validator: (value) => validator.isEmail(value),
    message: 'Invalid email format'
  },
  username: {
    required: true,
    validator: (value) => validator.isLength(value, { min: 2, max: 50 }) && 
                          validator.matches(value, /^[a-zA-Z0-9_-]+$/),
    message: 'Username must be 2-50 characters and contain only letters, numbers, underscore, and dash'
  },
  password: {
    required: true,
    validator: (value) => validator.isLength(value, { min: 8, max: 128 }),
    message: 'Password must be 8-128 characters long'
  },
  phoneNumber: {
    required: false,
    validator: (value) => !value || validator.isMobilePhone(value),
    message: 'Invalid phone number format'
  },
  phonenumber: {
    required: false,
    validator: (value) => !value || validator.isMobilePhone(value),
    message: 'Invalid phone number format'
  },
  mongoId: {
    required: true,
    validator: (value) => validator.isMongoId(value),
    message: 'Invalid ID format'
  },
  slug: {
    required: true,
    validator: (value) => validator.matches(value, /^[a-z0-9-]+$/),
    message: 'Slug must contain only lowercase letters, numbers, and dashes'
  },
  title: {
    required: true,
    validator: (value) => validator.isLength(value, { min: 1, max: 255 }),
    message: 'Title must be 1-255 characters long'
  },
  content: {
    required: false,
    validator: (value) => !value || validator.isLength(value, { max: 50000 }),
    message: 'Content must be less than 50,000 characters'
  },
  url: {
    required: false,
    validator: (value) => !value || validator.isURL(value),
    message: 'Invalid URL format'
  },
  // New validation rules for posts and categories
  postTitle: {
    required: true,
    validator: (value) => validator.isLength(value, { min: 3, max: 255 }) && 
                          !validator.contains(value, '<script>'),
    message: 'Post title must be 3-255 characters and cannot contain script tags'
  },
  postContent: {
    required: false,
    validator: (value) => !value || validator.isLength(value, { max: 100000 }),
    message: 'Post content must be less than 100,000 characters'
  },
  categoryName: {
    required: true,
    validator: (value) => validator.isLength(value, { min: 2, max: 100 }) && 
                          validator.matches(value, /^[a-zA-Z0-9\s\-_àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]+$/),
    message: 'Category name must be 2-100 characters and contain only letters, numbers, spaces, dashes, underscores, and Vietnamese characters'
  },
  role: {
    required: false,
    validator: (value) => !value || ['user', 'editor', 'manager', 'admin'].includes(value),
    message: 'Invalid role specified'
  },
  boolean: {
    required: false,
    validator: (value) => value === undefined || typeof value === 'boolean' || value === 'true' || value === 'false',
    message: 'Value must be boolean'
  },
  number: {
    required: false,
    validator: (value) => !value || validator.isNumeric(value.toString()),
    message: 'Value must be a number'
  },
  description: {
    required: false,
    validator: (value) => !value || validator.isLength(value, { max: 1000 }),
    message: 'Description must be less than 1000 characters'
  },  twoFactorCode: {
    required: true,
    validator: (value) => validator.isLength(value, { min: 6, max: 6 }) && validator.isNumeric(value),
    message: '2FA code must be exactly 6 digits'
  },
  // User profile validation rules
  fullName: {
    required: false,
    validator: (value) => !value || (validator.isLength(value, { min: 2, max: 100 }) && 
                          validator.matches(value, /^[a-zA-Z\s\-àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]+$/)),
    message: 'Full name must be 2-100 characters and contain only letters, spaces, dashes, and Vietnamese characters'
  },
  address: {
    required: false,
    validator: (value) => !value || validator.isLength(value, { max: 500 }),
    message: 'Address must be less than 500 characters'
  },
  city: {
    required: false,
    validator: (value) => !value || (validator.isLength(value, { min: 2, max: 100 }) && 
                          validator.matches(value, /^[a-zA-Z\s\-àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]+$/)),
    message: 'City must be 2-100 characters and contain only letters, spaces, dashes, and Vietnamese characters'
  },
  zipCode: {
    required: false,
    validator: (value) => !value || validator.matches(value, /^[0-9]{5,10}$/),
    message: 'Zip code must be 5-10 digits'
  },
  storeName: {
    required: false,
    validator: (value) => !value || (validator.isLength(value, { min: 2, max: 150 }) && 
                          !validator.contains(value, '<script>')),
    message: 'Store name must be 2-150 characters and cannot contain script tags'
  },
  bio: {
    required: false,
    validator: (value) => !value || validator.isLength(value, { max: 2000 }),
    message: 'Bio must be less than 2000 characters'
  },
  menuName: {
    required: true,
    validator: (value) => validator.isLength(value, { min: 1, max: 100 }) && 
                          !validator.contains(value, '<script>'),
    message: 'Menu name must be 1-100 characters and cannot contain script tags'
  }
};

// Sanitize input data
const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    // Remove null bytes and control characters
    input = input.replace(/\0/g, '');
    input = input.replace(/[\x00-\x1F\x7F]/g, '');
    
    // Trim whitespace
    input = input.trim();
    
    // Apply XSS protection
    input = xss(input, {
      whiteList: {}, // Remove all HTML tags
      stripIgnoreTag: true,
      stripIgnoreTagBody: ['script']
    });
    
    return input;
  } else if (typeof input === 'object' && input !== null) {
    const sanitized = {};
    for (const key in input) {
      if (input.hasOwnProperty(key)) {
        sanitized[key] = sanitizeInput(input[key]);
      }
    }
    return sanitized;
  } else if (Array.isArray(input)) {
    return input.map(item => sanitizeInput(item));
  }
  
  return input;
};

// Validate single field
const validateField = (value, fieldName, rules) => {
  const rule = rules[fieldName];
  if (!rule) return null;
  
  // Check if required field is present
  if (rule.required && (!value || value === '')) {
    return `${fieldName} is required`;
  }
  
  // Skip validation if field is not required and empty
  if (!rule.required && (!value || value === '')) {
    return null;
  }
  
  // Apply validator
  if (rule.validator && !rule.validator(value)) {
    return rule.message || `Invalid ${fieldName}`;
  }
  
  return null;
};

// Validate multiple fields
const validateFields = (data, rules) => {
  const errors = {};
  
  for (const fieldName in rules) {
    const error = validateField(data[fieldName], fieldName, rules);
    if (error) {
      errors[fieldName] = error;
    }
  }
  
  return Object.keys(errors).length > 0 ? errors : null;
};

// Generic validation middleware factory
const createValidationMiddleware = (fieldRules) => {
  return (req, res, next) => {
    try {
      // Sanitize input
      if (req.body) {
        req.body = sanitizeInput(req.body);
      }
      if (req.query) {
        req.query = sanitizeInput(req.query);
      }
      if (req.params) {
        req.params = sanitizeInput(req.params);
      }
      
      // Validate fields
      const errors = validateFields(req.body, fieldRules);
      if (errors) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: errors
        });
      }
      
      next();
    } catch (error) {
      console.error('Validation middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal validation error'
      });
    }
  };
};

// Pre-defined validation middleware for common use cases
const userRegistrationValidation = createValidationMiddleware({
  email: validationRules.email,
  username: validationRules.username,
  password: validationRules.password,
  phoneNumber: validationRules.phoneNumber
});

const userLoginValidation = createValidationMiddleware({
  email: validationRules.email,
  password: validationRules.password
});

// Post validation middleware
const postValidation = createValidationMiddleware({
  title: validationRules.postTitle,
  slug: validationRules.slug,
  content: validationRules.postContent,
  short: validationRules.description
});

const postUpdateValidation = createValidationMiddleware({
  title: validationRules.postTitle,
  slug: validationRules.slug,
  content: validationRules.postContent,
  short: validationRules.description,
  isActive: validationRules.boolean,
  isFeature: validationRules.boolean,
  index: validationRules.number
});

const categoryValidation = createValidationMiddleware({
  name: validationRules.categoryName,
  slug: validationRules.slug,
  content: validationRules.content,
  index: validationRules.number,
  isFeature: validationRules.boolean,
  isDefault: validationRules.boolean,
  block: validationRules.boolean
});

const pageValidation = createValidationMiddleware({
  title: validationRules.title,
  slug: validationRules.slug,
  desc: validationRules.content,
  short: validationRules.description,
  isActive: validationRules.boolean,
  index: validationRules.number
});



const twoFactorValidation = createValidationMiddleware({
  code: validationRules.twoFactorCode,
  userId: validationRules.mongoId
});

const settingsValidation = createValidationMiddleware({
  title: validationRules.title,
  desc: validationRules.description,
  email: validationRules.email
});



const mongoIdValidation = (req, res, next) => {
  const id = req.params.id;

  if (!id) {
    return res.status(400).json({
      success: false,
      message: 'ID parameter is required'
    });
  }

  if (!validator.isMongoId(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid ID format'
    });
  }

  next();
};

const userProfileValidation = createValidationMiddleware({
  username: validationRules.username,
  email: validationRules.email,
  fullName: validationRules.fullName,
  phoneNumber: validationRules.phoneNumber,
  bio: validationRules.bio
});

const storeValidation = createValidationMiddleware({
  storeName: validationRules.storeName,
  address: validationRules.address,
  city: validationRules.city,
  zipCode: validationRules.zipCode,
  phoneNumber: validationRules.phoneNumber,
  description: validationRules.description
});

const menuValidation = createValidationMiddleware({
  name: validationRules.menuName,
  url: validationRules.url,
  isActive: validationRules.boolean,
  index: validationRules.number
});

module.exports = {
  createValidationMiddleware,
  validateField,
  validateFields,
  sanitizeInput,
  validationRules,
  // Pre-defined middleware
  userRegistrationValidation,
  userLoginValidation,
  postValidation,
  postUpdateValidation,
  categoryValidation,
  pageValidation,
  twoFactorValidation,
  settingsValidation,
  mongoIdValidation,
  userProfileValidation,
  storeValidation,
  menuValidation
};
