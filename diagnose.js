#!/usr/bin/env node

/**
 * Diagnostic script to check server configuration and dependencies
 */

require('dotenv').config();

console.log('🔍 Diagnosing server configuration...\n');

// Check environment variables
console.log('📋 Environment Variables:');
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`PORT: ${process.env.PORT}`);
console.log(`MONGO_URL: ${process.env.MONGO_URL}`);
console.log(`NEXT_PUBLIC_API_ENDPOINT: ${process.env.NEXT_PUBLIC_API_ENDPOINT}`);
console.log(`CORS_ORIGINS: ${process.env.CORS_ORIGINS}\n`);

// Check required dependencies
console.log('📦 Checking dependencies...');
const requiredDeps = [
  'express',
  'next',
  'mongoose',
  'dotenv',
  'cors',
  'passport',
  'morgan'
];

let missingDeps = [];
for (const dep of requiredDeps) {
  try {
    require(dep);
    console.log(`✅ ${dep}`);
  } catch (err) {
    console.log(`❌ ${dep} - ${err.message}`);
    missingDeps.push(dep);
  }
}

if (missingDeps.length > 0) {
  console.log(`\n🚨 Missing dependencies: ${missingDeps.join(', ')}`);
  console.log('Run: npm install');
}

// Test MongoDB connection
console.log('\n🔌 Testing MongoDB connection...');
const mongoose = require('mongoose');

mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  serverSelectionTimeoutMS: 5000 // 5 second timeout
}).then(() => {
  console.log('✅ MongoDB connection successful');
  mongoose.disconnect();
}).catch((err) => {
  console.log('❌ MongoDB connection failed:', err.message);
  console.log('\n💡 Possible solutions:');
  console.log('1. Make sure MongoDB is running');
  console.log('2. Check MONGO_URL in .env file');
  console.log('3. For production, use MongoDB Atlas or hosted MongoDB');
});

// Check file system permissions
console.log('\n📁 Checking file system...');
const fs = require('fs');
const path = require('path');

const checkPaths = [
  './server/uploads',
  './server/backend-app.js',
  './.next',
  './package.json'
];

for (const checkPath of checkPaths) {
  try {
    if (fs.existsSync(checkPath)) {
      const stats = fs.statSync(checkPath);
      console.log(`✅ ${checkPath} (${stats.isDirectory() ? 'directory' : 'file'})`);
    } else {
      console.log(`❌ ${checkPath} - not found`);
    }
  } catch (err) {
    console.log(`❌ ${checkPath} - ${err.message}`);
  }
}

console.log('\n🔍 Diagnosis complete!');
