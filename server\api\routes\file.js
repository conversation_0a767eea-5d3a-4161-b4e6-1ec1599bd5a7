import express from "express";
import path from "path";
const passport = require('passport');
const { createSecureUpload, getAllowedExtensions } = require('../middleware/secureFileUpload');

const router = express.Router();

const uploadDir = path.join(__dirname, "../../uploads/file");

// Create secure upload middleware
const secureUpload = createSecureUpload(uploadDir);

// Upload route with enhanced security
router.post("/upload", passport.authenticate('user', { session: false }), (req, res) => {
  secureUpload.single("file")(req, res, (err) => {
    if (err) {
      console.error('File upload error:', err.message);
      return res.status(400).json({ 
        success: false, 
        message: err.message || "File upload failed",
        allowedTypes: getAllowedExtensions()
      });
    }
    
    if (!req.file) {
      return res.status(400).json({ 
        success: false, 
        message: "No file uploaded",
        allowedTypes: getAllowedExtensions()
      });
    }
    
    res.json({
      success: true,
      fileUrl: `/uploads/file/${req.file.filename}`,
      originalName: req.file.originalname,
      size: req.file.size
    });
  });
});

// Upload multiple files route with enhanced security
router.post("/multi", passport.authenticate('user', { session: false }), (req, res) => {
  secureUpload.array("files", 10)(req, res, (err) => {
    if (err) {
      console.error('Multiple file upload error:', err.message);
      return res.status(400).json({ 
        success: false, 
        message: err.message || "File upload failed",
        allowedTypes: getAllowedExtensions()
      });
    }
    
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: "No files uploaded",
        allowedTypes: getAllowedExtensions()
      });
    }

    // Get uploaded file URLs with metadata
    const fileData = req.files.map(file => ({
      url: `/uploads/file/${file.filename}`,
      originalName: file.originalname,
      size: file.size
    }));

    res.json({
      success: true,
      files: fileData,
      count: req.files.length
    });
  });
});

module.exports = router;
