const mongoose = require("mongoose");
const DepartmentFile = require("../models/departmentFile");
const Department = require("../models/department");
const User = require("../models/user");
const { ForbiddenError } = require("@casl/ability");
const defineAbilityFor = require("../permissions/abilities");
const multer = require("multer");
const path = require("path");
const fs = require("fs").promises;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const departmentId = req.params.departmentId;
    const uploadPath = path.join(__dirname, '../../uploads/departments', departmentId);
    
    try {
      await fs.mkdir(uploadPath, { recursive: true });
      cb(null, uploadPath);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    // Add file type restrictions if needed
    cb(null, true);
  }
});

// Get all files for a department
exports.getDepartmentFiles = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { 
      page = 1, 
      perPage = 20, 
      category, 
      folder = "/",
      tags,
      search,
      uploadedBy,
      sort = "uploadedAt"
    } = req.query;

    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canAccess = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId) ||
                     (req.user.department?.toString() === departmentId);

    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Build query options
    const options = {
      category,
      folder,
      tags: tags ? tags.split(',') : undefined,
      search,
      uploadedBy,
      sort: { [sort]: -1 }
    };

    const query = DepartmentFile.findByDepartment(departmentId, options);
    
    const onTotal = DepartmentFile.countDocuments({
      department: departmentId,
      status: 'active',
      ...(category && { category }),
      ...(folder && { folder }),
      ...(uploadedBy && { uploadedBy })
    });

    const onFiles = query
      .skip(perPage * page - perPage)
      .limit(perPage);

    const [total, files] = await Promise.all([onTotal, onFiles]);

    res.json({
      success: true,
      files,
      total,
      page: parseInt(page),
      perPage: parseInt(perPage),
      totalPages: Math.ceil(total / perPage)
    });

  } catch (error) {
    console.error('Error getting department files:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Upload file
exports.uploadFile = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { description, category, tags, folder = "/", visibility = "department" } = req.body;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "No file uploaded"
      });
    }

    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canUpload = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId) ||
                     (req.user.department?.toString() === departmentId && req.user.permissions.includes('dept_file_upload'));

    if (!canUpload) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    const file = new DepartmentFile({
      department: departmentId,
      fileName: req.file.filename,
      originalName: req.file.originalname,
      description: description?.trim(),
      filePath: req.file.path,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      category,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      folder,
      visibility,
      uploadedBy: req.user._id
    });

    await file.save();

    // Populate the saved file
    await file.populate([
      { path: 'department', select: 'name code' },
      { path: 'uploadedBy', select: 'username email' }
    ]);

    res.status(201).json({
      success: true,
      file,
      message: "File uploaded successfully"
    });

  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Get single file
exports.getFile = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await DepartmentFile.findById(id)
      .populate('department', 'name code')
      .populate('uploadedBy', 'username email')
      .populate('accessPermissions.user', 'username email')
      .populate('shares.sharedWith', 'username email')
      .populate('shares.sharedBy', 'username email')
      .populate('comments.user', 'username email');

    if (!file) {
      return res.status(404).json({
        success: false,
        message: "File not found"
      });
    }

    // Check access permission
    if (!file.canAccess(req.user, 'view')) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Track view
    await file.addView(
      req.user._id,
      req.ip || req.connection.remoteAddress,
      req.get('User-Agent')
    );

    res.json({
      success: true,
      file
    });

  } catch (error) {
    console.error('Error getting file:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Download file
exports.downloadFile = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await DepartmentFile.findById(id);
    if (!file) {
      return res.status(404).json({
        success: false,
        message: "File not found"
      });
    }

    // Check access permission
    if (!file.canAccess(req.user, 'download')) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Track download
    await file.addDownload(
      req.user._id,
      req.ip || req.connection.remoteAddress,
      req.get('User-Agent')
    );

    // Send file
    res.download(file.filePath, file.originalName);

  } catch (error) {
    console.error('Error downloading file:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Update file metadata
exports.updateFile = async (req, res) => {
  try {
    const { id } = req.params;
    const { description, category, tags, folder, visibility } = req.body;

    const file = await DepartmentFile.findById(id);
    if (!file) {
      return res.status(404).json({
        success: false,
        message: "File not found"
      });
    }

    // Check edit permission
    if (!file.canAccess(req.user, 'edit')) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Update fields
    if (description !== undefined) file.description = description?.trim();
    if (category) file.category = category;
    if (tags) file.tags = tags.split(',').map(tag => tag.trim());
    if (folder) file.folder = folder;
    if (visibility) file.visibility = visibility;

    await file.save();

    res.json({
      success: true,
      file,
      message: "File updated successfully"
    });

  } catch (error) {
    console.error('Error updating file:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Delete file
exports.deleteFile = async (req, res) => {
  try {
    const { id } = req.params;

    const file = await DepartmentFile.findById(id);
    if (!file) {
      return res.status(404).json({
        success: false,
        message: "File not found"
      });
    }

    // Check delete permission
    if (!file.canAccess(req.user, 'delete')) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Soft delete
    file.status = 'deleted';
    await file.save();

    res.json({
      success: true,
      message: "File deleted successfully"
    });

  } catch (error) {
    console.error('Error deleting file:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Share file
exports.shareFile = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, permissions = ['view'], expiresAt } = req.body;

    const file = await DepartmentFile.findById(id);
    if (!file) {
      return res.status(404).json({
        success: false,
        message: "File not found"
      });
    }

    // Check share permission
    if (!file.canAccess(req.user, 'share')) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    const targetUser = await User.findById(userId);
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    await file.shareWith(userId, req.user._id, permissions, expiresAt ? new Date(expiresAt) : null);

    res.json({
      success: true,
      message: "File shared successfully"
    });

  } catch (error) {
    console.error('Error sharing file:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

module.exports = {
  upload,
  getDepartmentFiles: exports.getDepartmentFiles,
  uploadFile: exports.uploadFile,
  getFile: exports.getFile,
  downloadFile: exports.downloadFile,
  updateFile: exports.updateFile,
  deleteFile: exports.deleteFile,
  shareFile: exports.shareFile
};
