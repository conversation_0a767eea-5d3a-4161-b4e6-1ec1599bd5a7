import authApiRequest from '@/apiRequests/auth'
import { HttpError } from '@/lib/http'
import { cookies } from 'next/headers'


export async function POST(request: Request) {
  const res = await request.json()
  const force = res.force as boolean | undefined
 
  if (force) {
    return Response.json(
      {
        message: 'Buộc đăng xuất thành công'
      },
      {
        status: 200,
        headers: {
          'Set-Cookie': [
            `sessionToken=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax`,
            `userRole=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax`,
            `sessionTokenExpiresAt=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax`
          ]
        }
      }
    )
  }
  const cookieStore = cookies()
  const sessionToken = cookieStore.get('sessionToken')
  const sessionTokenExpiresAt = cookieStore.get('sessionTokenExpiresAt')
  
  if (!sessionToken) {
    return Response.json(
      { message: '<PERSON>hông nhận được session token' },
      {
        status: 401
      }
    )
  }
 
  try {
   
    const result = await authApiRequest.logoutFromNextServerToServer(
      {sessionToken: sessionToken.value }
    )
  
    return Response.json(result.payload, {
      status: 200,
      headers: {
        'Set-Cookie': [
          `sessionToken=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax`,
          `userRole=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax`,
          `sessionTokenExpiresAt=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax`
        ]
      }
    });
    
  } catch (error) {
    if (error instanceof HttpError) {
      return Response.json(error.payload, {
        status: error.status
      })
    } else {
      return Response.json(
        {
          message: 'Lỗi không xác định'
        },
        {
          status: 500
        }
      )
    }
  }
}