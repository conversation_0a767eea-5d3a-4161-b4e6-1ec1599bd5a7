const mongoose = require("mongoose");
const User = require("../models/user");
const Department = require("../models/department");
require("dotenv").config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

const migrateToDepartments = async () => {
  try {
    console.log("🚀 Starting migration to department-based system...");

    // Step 1: Create default departments based on existing roles
    console.log("\n📁 Creating default departments...");
    
    const defaultDepartments = [
      {
        name: "<PERSON>u<PERSON><PERSON> Tr<PERSON>",
        code: "ADMIN",
        description: "Phòng ban quản trị hệ thống",
        defaultPermissions: [
          "system_admin_full_access",
          "system_departments_manage",
          "system_users_manage",
          "system_settings_manage",
          "system_analytics_view"
        ]
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON> Lý",
        code: "MANAGER",
        description: "Phòng ban quản lý",
        defaultPermissions: [
          "dept_file_view",
          "dept_file_upload",
          "dept_file_delete",
          "dept_analytics_view",
          "dept_reports_view",
          "dept_reports_create"
        ]
      },
      {
        name: "Biên Tập",
        code: "EDITOR",
        description: "Phòng ban biên tập nội dung",
        defaultPermissions: [
          "dept_file_view",
          "dept_file_upload",
          "dept_analytics_view"
        ]
      },
      {
        name: "Nhân Viên",
        code: "USER",
        description: "Phòng ban nhân viên chung",
        defaultPermissions: [
          "dept_file_view",
          "dept_file_upload"
        ]
      }
    ];

    const createdDepartments = {};
    
    for (const deptData of defaultDepartments) {
      const existingDept = await Department.findOne({ code: deptData.code });
      if (!existingDept) {
        const department = new Department(deptData);
        await department.save();
        createdDepartments[deptData.code] = department;
        console.log(`✅ Created department: ${deptData.name} (${deptData.code})`);
      } else {
        createdDepartments[deptData.code] = existingDept;
        console.log(`ℹ️  Department already exists: ${deptData.name} (${deptData.code})`);
      }
    }

    // Step 2: Migrate users based on their current roles
    console.log("\n👥 Migrating users...");
    
    const users = await User.find({});
    let migratedCount = 0;
    let adminCount = 0;
    let errorCount = 0;

    for (const user of users) {
      try {
        let updateData = {};
        
        // Handle different user roles
        switch (user.rule) {
          case 'admin':
            // System admins don't belong to departments
            updateData = {
              systemRole: 'admin',
              department: null,
              isDepartmentAdmin: false,
              permissions: [
                ...user.permissions,
                'system_admin_full_access'
              ]
            };
            adminCount++;
            break;
            
          case 'manager':
            updateData = {
              systemRole: null,
              department: createdDepartments.MANAGER._id,
              isDepartmentAdmin: true, // Managers become department admins
              permissions: [
                ...user.permissions,
                ...createdDepartments.MANAGER.defaultPermissions
              ]
            };
            
            // Set this user as department admin
            await Department.findByIdAndUpdate(
              createdDepartments.MANAGER._id,
              { departmentAdmin: user._id }
            );
            break;
            
          case 'editor':
            updateData = {
              systemRole: null,
              department: createdDepartments.EDITOR._id,
              isDepartmentAdmin: false,
              permissions: [
                ...user.permissions,
                ...createdDepartments.EDITOR.defaultPermissions
              ]
            };
            break;
            
          case 'user':
          default:
            updateData = {
              systemRole: null,
              department: createdDepartments.USER._id,
              isDepartmentAdmin: false,
              permissions: [
                ...user.permissions,
                ...createdDepartments.USER.defaultPermissions
              ]
            };
            break;
        }

        // Remove duplicates from permissions
        updateData.permissions = [...new Set(updateData.permissions)];

        // Update user
        await User.findByIdAndUpdate(user._id, updateData);
        
        console.log(`✅ Migrated user: ${user.email} (${user.rule} -> ${updateData.systemRole || 'department user'})`);
        migratedCount++;
        
      } catch (error) {
        console.error(`❌ Error migrating user ${user.email}:`, error.message);
        errorCount++;
      }
    }

    // Step 3: Create indexes for better performance
    console.log("\n🔍 Creating database indexes...");
    
    await User.collection.createIndex({ department: 1 });
    await User.collection.createIndex({ isDepartmentAdmin: 1 });
    await User.collection.createIndex({ systemRole: 1 });
    await User.collection.createIndex({ department: 1, isDepartmentAdmin: 1 });
    
    console.log("✅ Database indexes created");

    // Step 4: Validation
    console.log("\n🔍 Validating migration...");
    
    const totalUsers = await User.countDocuments();
    const systemAdmins = await User.countDocuments({ systemRole: 'admin' });
    const departmentUsers = await User.countDocuments({ department: { $ne: null } });
    const departmentAdmins = await User.countDocuments({ isDepartmentAdmin: true });
    
    console.log("\n📊 Migration Summary:");
    console.log(`Total users: ${totalUsers}`);
    console.log(`System admins: ${systemAdmins}`);
    console.log(`Department users: ${departmentUsers}`);
    console.log(`Department admins: ${departmentAdmins}`);
    console.log(`Successfully migrated: ${migratedCount}`);
    console.log(`Errors: ${errorCount}`);
    
    // List departments with member counts
    console.log("\n🏢 Department Summary:");
    for (const [code, dept] of Object.entries(createdDepartments)) {
      const memberCount = await User.countDocuments({ department: dept._id });
      const adminUser = dept.departmentAdmin ? 
        await User.findById(dept.departmentAdmin).select('email') : null;
      
      console.log(`${dept.name} (${code}): ${memberCount} members${adminUser ? `, Admin: ${adminUser.email}` : ''}`);
    }

    console.log("\n🎉 Migration completed successfully!");
    
  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  }
};

// Run migration if this script is executed directly
if (require.main === module) {
  migrateToDepartments()
    .then(() => {
      console.log("Migration completed. Exiting...");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Migration failed:", error);
      process.exit(1);
    });
}

module.exports = migrateToDepartments;
