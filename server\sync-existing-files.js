const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const File = require('./api/models/file');

// Normalize path separators for web URLs
const normalizeWebPath = (filePath) => {
  if (!filePath) return '';
  return filePath.replace(/\\/g, '/');
};

// Get file type based on extension
const getFileType = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  
  if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext)) {
    return 'image';
  } else if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'].includes(ext)) {
    return 'video';
  } else if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'].includes(ext)) {
    return 'document';
  } else {
    return 'other';
  }
};

// Get MIME type based on extension
const getMimeType = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',
    '.mp4': 'video/mp4',
    '.avi': 'video/x-msvideo',
    '.mov': 'video/quicktime',
    '.wmv': 'video/x-ms-wmv',
    '.flv': 'video/x-flv',
    '.webm': 'video/webm',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.txt': 'text/plain'
  };
  
  return mimeTypes[ext] || 'application/octet-stream';
};

// Recursively scan directory for files
const scanDirectory = (dirPath, baseDir = dirPath) => {
  const files = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        // Recursively scan subdirectories
        files.push(...scanDirectory(fullPath, baseDir));
      } else if (stats.isFile()) {
        const relativePath = path.relative(baseDir, fullPath);
        const normalizedPath = normalizeWebPath(relativePath);
        
        files.push({
          filename: item,
          relativePath: normalizedPath,
          fullPath: fullPath,
          size: stats.size,
          mtime: stats.mtime
        });
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
  
  return files;
};

// Main sync function
const syncExistingFiles = async () => {
  try {
    console.log('🔄 Starting file synchronization...');
    
    const uploadsDir = path.join(__dirname, 'uploads');
    
    if (!fs.existsSync(uploadsDir)) {
      console.log('❌ Uploads directory not found:', uploadsDir);
      return;
    }
    
    // Scan all files in uploads directory
    console.log('📁 Scanning uploads directory...');
    const physicalFiles = scanDirectory(uploadsDir);
    console.log(`📊 Found ${physicalFiles.length} physical files`);
    
    // Get all files from database
    const dbFiles = await File.find({}).select('filename relativePath').lean();
    console.log(`💾 Found ${dbFiles.length} files in database`);
    
    // Find files that exist physically but not in database
    const dbFilePaths = new Set(dbFiles.map(f => f.relativePath || f.filename));
    const missingFiles = physicalFiles.filter(file => 
      !dbFilePaths.has(file.relativePath) && !dbFilePaths.has(file.filename)
    );
    
    console.log(`🔍 Found ${missingFiles.length} files missing from database`);
    
    if (missingFiles.length === 0) {
      console.log('✅ All files are already synchronized');
      return;
    }
    
    // Add missing files to database
    let addedCount = 0;
    const defaultUser = await mongoose.model('User').findOne({ rule: 'admin' });
    
    if (!defaultUser) {
      console.log('❌ No admin user found for file ownership');
      return;
    }
    
    for (const file of missingFiles) {
      try {
        const newFile = new File({
          filename: file.filename,
          originalName: file.filename,
          relativePath: file.relativePath,
          path: file.fullPath,
          size: file.size,
          type: getFileType(file.filename),
          mimetype: getMimeType(file.filename),
          url: `/uploads/${file.relativePath}`,
          uploadedBy: defaultUser._id,
          uploadedAt: file.mtime,
          isActive: true,
          description: `File được đồng bộ từ hệ thống: ${file.filename}`
        });
        
        await newFile.save();
        addedCount++;
        
        if (addedCount % 10 === 0) {
          console.log(`📝 Added ${addedCount}/${missingFiles.length} files...`);
        }
      } catch (error) {
        console.error(`❌ Error adding file ${file.filename}:`, error.message);
      }
    }
    
    console.log(`✅ File synchronization completed! Added ${addedCount} files to database`);
    
  } catch (error) {
    console.error('❌ Error in file synchronization:', error);
    throw error;
  }
};

module.exports = {
  syncExistingFiles,
  scanDirectory,
  normalizeWebPath
};
