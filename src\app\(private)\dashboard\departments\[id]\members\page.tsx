"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { toast } from "react-toastify";
import Link from "next/link";
import { ArrowLeft, Users, UserPlus, Search } from "react-feather";
import departmentApiRequest, { Department, DepartmentMember } from "@/apiRequests/department";
import PermissionGuard from "@/components/PermissionGuard";

export default function DepartmentMembersPage() {
  const params = useParams();
  const router = useRouter();
  const [department, setDepartment] = useState<Department | null>(null);
  const [members, setMembers] = useState<DepartmentMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalMembers, setTotalMembers] = useState(0);
  const [removingMember, setRemovingMember] = useState<string | null>(null);
  const perPage = 10;

  const departmentId = params.id as string;

  const fetchDepartment = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.getDepartment(departmentId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        setDepartment(response.payload.department);
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error: any) {
      console.error("Error fetching department:", error);
      toast.error("Có lỗi xảy ra khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    }
  };

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.getDepartmentMembers(
        departmentId,
        { page: currentPage, perPage },
        sessionToken
      );
      
      if (response && response.payload && response.payload.success) {
        setMembers(response.payload.members);
        setTotalMembers(response.payload.total);
      } else {
        toast.error("Không thể tải danh sách thành viên");
      }
    } catch (error: any) {
      console.error("Error fetching members:", error);
      toast.error("Có lỗi xảy ra khi tải danh sách thành viên");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
      fetchMembers();
    }
  }, [departmentId, currentPage]);

  const handleRemoveMember = async (userId: string, username: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa ${username} khỏi phòng ban?`)) {
      return;
    }

    try {
      setRemovingMember(userId);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.removeMember(departmentId, userId, sessionToken);

      if (response && response.payload && response.payload.success) {
        toast.success("Đã xóa thành viên khỏi phòng ban");
        fetchMembers(); // Refresh the list
      } else {
        toast.error("Không thể xóa thành viên. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error removing member:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi xóa thành viên";
      toast.error(errorMessage);
    } finally {
      setRemovingMember(null);
    }
  };

  const filteredMembers = members.filter(member =>
    member.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(totalMembers / perPage);

  if (!department) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-gray-600">Đang tải thông tin phòng ban...</div>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermissions={["system_departments_manage", "dept_members_manage"]} fallbackPath="/dashboard">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/departments/${departmentId}`}
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} className="mr-2" />
              Quay lại
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Thành viên phòng ban</h1>
              <p className="text-gray-600">{department.name} - {department.code}</p>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <Link
              href={`/dashboard/departments/${departmentId}/members/create`}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <UserPlus size={16} className="mr-2" />
              Tạo thành viên mới
            </Link>
            <Link
              href={`/dashboard/departments/${departmentId}/members/add`}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <UserPlus size={16} className="mr-2" />
              Thêm thành viên có sẵn
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Tìm kiếm thành viên..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Tổng thành viên</p>
                <p className="text-2xl font-semibold text-gray-900">{totalMembers}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Giới hạn</p>
                <p className="text-2xl font-semibold text-gray-900">{department.maxMembers}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Còn lại</p>
                <p className="text-2xl font-semibold text-gray-900">{department.maxMembers - totalMembers}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Members Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Danh sách thành viên</h3>
            
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-gray-600">Đang tải danh sách thành viên...</div>
              </div>
            ) : filteredMembers.length === 0 ? (
              <div className="text-center py-8">
                <Users size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có thành viên</h3>
                <p className="text-gray-600">Phòng ban này chưa có thành viên nào.</p>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thành viên
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vai trò
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Ngày tham gia
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Trạng thái
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Thao tác
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredMembers.map((member) => (
                        <tr key={member._id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                  <span className="text-sm font-medium text-gray-700">
                                    {member.username.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{member.username}</div>
                                <div className="text-sm text-gray-500">{member.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              member.isDepartmentAdmin
                                ? 'bg-purple-100 text-purple-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {member.isDepartmentAdmin ? 'Quản lý phòng ban' : 'Thành viên'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {new Date(member.joinedAt).toLocaleDateString("vi-VN")}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                              Hoạt động
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              className="text-blue-600 hover:text-blue-900 mr-3"
                              onClick={() => toast.info("Chức năng chỉnh sửa sẽ được phát triển")}
                            >
                              Chỉnh sửa
                            </button>
                            <button
                              className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                              onClick={() => handleRemoveMember(member._id, member.username)}
                              disabled={removingMember === member._id}
                            >
                              {removingMember === member._id ? "Đang xóa..." : "Xóa"}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-6">
                    <div className="text-sm text-gray-700">
                      Hiển thị {(currentPage - 1) * perPage + 1} đến {Math.min(currentPage * perPage, totalMembers)} trong tổng số {totalMembers} thành viên
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Trước
                      </button>
                      <span className="px-3 py-1 text-sm font-medium text-gray-700">
                        Trang {currentPage} / {totalPages}
                      </span>
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Sau
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
}
