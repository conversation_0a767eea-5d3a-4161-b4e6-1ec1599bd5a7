const mongoose = require('mongoose');
require('dotenv').config();

// Import models
require('../api/models/file');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

const File = mongoose.model('File');

// Fix relative paths
async function fixRelativePaths() {
  try {
    console.log('🔄 Starting relative path fix...');
    
    const files = await File.find({});
    console.log(`📊 Found ${files.length} files to check`);
    
    let fixedFiles = 0;
    for (const file of files) {
      if (!file.relativePath && file.url) {
        // Extract relativePath from URL
        let relativePath = file.url.replace(/^\/+/, ''); // Remove leading slashes
        relativePath = relativePath.replace(/^uploads\//, ''); // Remove uploads/ prefix
        
        console.log(`📝 Fixing file: ${file.filename}`);
        console.log(`   URL: ${file.url}`);
        console.log(`   Setting relativePath: ${relativePath}`);
        
        file.relativePath = relativePath;
        await file.save();
        fixedFiles++;
      }
    }
    
    console.log(`✅ Fixed ${fixedFiles} files with relativePath`);
    
  } catch (error) {
    console.error('❌ Error fixing relative paths:', error);
    throw error;
  }
}

// Run fix
async function runFix() {
  try {
    console.log('🚀 Starting relative path fix script...');
    await fixRelativePaths();
    console.log('✅ Fix completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  }
}

// Handle MongoDB connection
mongoose.connection.on('connected', () => {
  console.log('📊 Connected to MongoDB');
  runFix();
});

mongoose.connection.on('error', (err) => {
  console.error('❌ MongoDB connection error:', err);
  process.exit(1);
});

mongoose.connection.on('disconnected', () => {
  console.log('📊 Disconnected from MongoDB');
});
