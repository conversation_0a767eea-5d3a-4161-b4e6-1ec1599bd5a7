import http from "@/lib/http";

export interface DepartmentReport {
  _id: string;
  department: {
    _id: string;
    name: string;
    code: string;
  };
  reportType: "monthly_summary" | "member_activity" | "performance_metrics" | "file_usage" | "custom_report";
  title: string;
  description?: string;
  reportPeriod: {
    startDate: string;
    endDate: string;
  };
  data: {
    memberStats: {
      totalMembers: number;
      activeMembers: number;
      newMembers: number;
      departmentAdmins: number;
    };
    activityStats: {
      totalLogins: number;
      averageSessionTime: number;
      filesUploaded: number;
      filesDownloaded: number;
      announcementsCreated: number;
      announcementsViewed: number;
    };
    performanceMetrics: {
      taskCompletionRate: number;
      averageResponseTime: number;
      userSatisfactionScore: number;
      productivityIndex: number;
    };
    fileStats: {
      totalFiles: number;
      totalFileSize: number;
      mostAccessedFiles: Array<{
        fileName: string;
        accessCount: number;
        lastAccessed: string;
      }>;
      fileTypeDistribution: Array<{
        fileType: string;
        count: number;
        totalSize: number;
      }>;
    };
    customData: any;
  };
  generatedBy: {
    _id: string;
    username: string;
    email: string;
  };
  generatedAt: string;
  status: "generating" | "completed" | "failed" | "archived";
  isPublic: boolean;
  tags: string[];
  exportFormats: Array<{
    format: "pdf" | "excel" | "csv" | "json";
    filePath: string;
    generatedAt: string;
    fileSize: number;
  }>;
  sharedWith: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    permissions: Array<"view" | "download" | "edit" | "share">;
    sharedAt: string;
  }>;
  comments: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    content: string;
    createdAt: string;
  }>;
  schedule?: {
    isRecurring: boolean;
    frequency?: "daily" | "weekly" | "monthly" | "quarterly" | "yearly";
    nextGeneration?: string;
    lastGeneration?: string;
  };
  createdAt: string;
  updatedAt: string;
  reportDuration?: number;
  totalActivityScore?: number;
}

export interface CreateReportRequest {
  reportType: string;
  title: string;
  description?: string;
  startDate: string;
  endDate: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface UpdateReportRequest {
  title?: string;
  description?: string;
  isPublic?: boolean;
  tags?: string[];
}

export interface ReportListResponse {
  success: boolean;
  reports: DepartmentReport[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export interface ReportResponse {
  success: boolean;
  report: DepartmentReport;
  message?: string;
}

export interface CommentResponse {
  success: boolean;
  comments: DepartmentReport['comments'];
  message: string;
}

const departmentReportApiRequest = {
  // Get all reports for a department
  getDepartmentReports: (
    departmentId: string,
    params: {
      page?: number;
      perPage?: number;
      reportType?: string;
      status?: string;
      startDate?: string;
      endDate?: string;
    },
    sessionToken: string
  ) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    
    return http.get<ReportListResponse>(
      `/api/administrator/departments/${departmentId}/reports?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    );
  },

  // Get single report
  getReport: (id: string, sessionToken: string) =>
    http.get<ReportResponse>(`/api/administrator/departments/reports/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Create new report
  createReport: (departmentId: string, data: CreateReportRequest, sessionToken: string) =>
    http.post<ReportResponse>(`/api/administrator/departments/${departmentId}/reports`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Update report
  updateReport: (id: string, data: UpdateReportRequest, sessionToken: string) =>
    http.put<ReportResponse>(`/api/administrator/departments/reports/${id}`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete report
  deleteReport: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(`/api/administrator/departments/reports/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Add comment to report
  addComment: (id: string, content: string, sessionToken: string) =>
    http.post<CommentResponse>(`/api/administrator/departments/reports/${id}/comments`, { content }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Export report (placeholder for future implementation)
  exportReport: (id: string, format: "pdf" | "excel" | "csv" | "json", sessionToken: string) =>
    http.get<Blob>(`/api/administrator/departments/reports/${id}/export/${format}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
      responseType: 'blob' as any,
    }),

  // Share report (placeholder for future implementation)
  shareReport: (
    id: string, 
    data: { 
      userId: string; 
      permissions: Array<"view" | "download" | "edit" | "share"> 
    }, 
    sessionToken: string
  ) =>
    http.post<{ success: boolean; message: string }>(`/api/administrator/departments/reports/${id}/share`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default departmentReportApiRequest;
