"use client";
import { usePermissions } from "@/hooks/usePermissions";
import { useAppContext } from "@/app/app-provider";
import { useState } from "react";

export default function PermissionsDebug() {
  const { user } = useAppContext();
  const { getAllPermissions, isAdmin } = usePermissions();
  const [isOpen, setIsOpen] = useState(false);

  if (!user) return null;

  const allPermissions = getAllPermissions();

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-blue-500 text-white px-3 py-2 rounded-full shadow-lg hover:bg-blue-600 text-sm"
      >
        Debug Permissions
      </button>
      
      {isOpen && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-300 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <div className="mb-3">
            <h3 className="font-bold text-gray-800">User Info</h3>
            <p className="text-sm text-gray-600">Username: {user.username}</p>
            <p className="text-sm text-gray-600">Role: {user.rule}</p>
            <p className="text-sm text-gray-600">Is Admin: {isAdmin ? 'Yes' : 'No'}</p>
          </div>
          
          <div className="mb-3">
            <h3 className="font-bold text-gray-800">Raw Permissions</h3>
            <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
              {user.permissions?.length ? (
                <pre>{JSON.stringify(user.permissions, null, 2)}</pre>
              ) : (
                'No permissions set'
              )}
            </div>
          </div>
          
          <div>
            <h3 className="font-bold text-gray-800">Effective Permissions</h3>
            <div className="space-y-1 max-h-40 overflow-y-auto">
              {allPermissions.map((permission) => (
                <div key={permission} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                  {permission}
                </div>
              ))}
            </div>
          </div>
          
          <button
            onClick={() => setIsOpen(false)}
            className="mt-3 w-full bg-gray-500 text-white py-1 rounded text-sm hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      )}
    </div>
  );
}
