"use client";

import { usePathname } from "next/navigation";
import React, { useState, useEffect } from "react";
import { User, Menu, X } from "react-feather";
import BottomMenu from "@/components/Navigation/BottomMenu";
import SideMobileMenu from "@/components/Navigation/SideMobileMenu";
import { useAppContext } from "@/app/app-provider";
import SearchNormal from "@/components/Navigation/SearchNormal";
import ProfileDropdown from "@/components/Navigation/ProfileDropdown";

/**
 * Header component for Ho Chi Minh City People's Court
 */
const VietnameseHeader: React.FC = () => {
  const { user } = useAppContext();
  const pathname = usePathname();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    document.body.style.position = "relative";
    document.body.style.overflowY = "auto";

    // Function to check if the screen width is mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile(); // Initial check
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, [pathname]);

  const getVietnamDate = () => {
    const date = new Date();
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      day: "numeric",
      month: "numeric",
      year: "numeric",
      timeZone: "Asia/Ho_Chi_Minh",
    };
    return new Intl.DateTimeFormat("vi-VN", options).format(date);
  };

  const isPrivateRoute = pathname.startsWith("/dashboard");

  return (
    <>
      {!isPrivateRoute && (
        <>
          {/* Vietnamese Supreme Court Header */}
          <header className="w-full hidden md:block" style={{ height: "130px" }}>
            <div 
              style={{ 
                width: "100%", 
                height: "130px", 
                position: "relative",
                backgroundImage: "-webkit-gradient(linear, 0% 100%, 0% 0%, color-stop(0.2, rgb(255, 255, 255)), color-stop(0.8, rgb(255, 251, 213)))"
              }}
            >
              <div 
                className="container mx-auto relative" 
                style={{ 
                  background: "url(/trongdong_1546498623413.png) no-repeat", 
                  height: "130px",
                  display: "flex",
                  alignItems: "center"
                }}
              >
                <div className="flex items-center ml-4">
                  <div>
                    <a href="/">
                      <img src="/favicon.ico" alt="Quốc huy" style={{ width: "90px", height: "97px" }} />
                    </a>
                  </div>
                  <div className="flex flex-col ml-3">
                    <div className="text-base md:text-lg text-blue-400 font-medium">CỔNG THÔNG TIN ĐIỆN TỬ</div>
                    <div className="text-red-600" style={{ 
                      fontFamily: '"Roboto Condensed", sans-serif',
                      fontSize: "44px",
                      fontWeight: 700,
                      fontStyle: "normal",
                      lineHeight: "48.4px",
                      color: "#dc2626 !important",
                      WebkitTextFillColor: "#dc2626 !important"
                    }}>TÒA ÁN NHÂN DÂN TP.HỒ CHÍ MINH</div>
                  </div>
                </div>
                
                {/* Phần tử ở góc phải */}
                <div className="absolute right-0 top-0 h-full">
                  <img 
                    src="/bg-header_1546498587110.png"
                    alt="Hình ảnh Tòa án"
                    style={{ height: "130px" }}
                  />
                </div>
                {/* Removed date display */}
                {/* <div className="absolute right-4 top-0 h-full flex items-center z-10">
                  <div className="text-right">
                    {user && <div className="text-blue-600 font-medium text-sm md:text-base lg:text-lg">Xin chào, {user.displayName}</div>}
                  </div>
                </div> */}
              </div>
            </div>
          </header>

          {/* Regular Navigation */}
          <nav
            id="top-nav"
            className="w-full z-20 border-b border-gray-200 dark:border-gray-600"
          >
            <div className="container flex flex-wrap items-center justify-between mx-auto">
              {/* Mobile Logo */}
              <div className="block md:hidden">
                <div className="flex items-center">
                  <div>
                    <a href="/">
                      <img src="/favicon.ico" alt="Quốc huy" style={{ width: "60px", height: "65px" }} />
                    </a>
                  </div>
                  <div className="flex flex-col ml-2">
                    <div className="text-xs text-blue-400 font-medium">CỔNG THÔNG TIN ĐIỆN TỬ</div>
                    <div className="text-sm font-bold text-red-600" style={{ 
                      color: "#dc2626 !important", 
                      WebkitTextFillColor: "#dc2626 !important" 
                    }}>TÒA ÁN NHÂN DÂN THÀNH PHỐ HỒ CHÍ MINH</div>
                  </div>
                </div>
              </div>
              
              <div className="w-1/3 hidden md:block"></div>
              <div className="right-block flex menu-search items-center justify-end w-auto ml-auto">
                {/* Mobile user profile button - Only show if user is logged in */}
                {user && (
                  <span className="md:hidden block">
                    <ProfileDropdown />
                  </span>
                )}
                
                {/* Desktop login button */}
                {/* <span className="md:block hidden ml-2">
                  {user && <ProfileDropdown />}
                </span> */}

                <div className="flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
                  <div className="drawer ml-2 block md:hidden">
                    <input id="my-drawer" type="checkbox" className="drawer-toggle" />
                    <div className="drawer-content">
                      <label htmlFor="my-drawer" className="cursor-pointer">
                        {/* <Menu /> */}
                      </label>
                    </div>
                    <div className="drawer-side z-50 overflow-y-auto">
                      <label htmlFor="my-drawer" aria-label="close sidebar" className="drawer-overlay"></label>
                      <div className="menu bg-base-200 text-base-content min-h-full md:w-2/3 lg:w-1/2 w-full p-4">
                        <label
                          htmlFor="my-drawer"
                          aria-label="close sidebar"
                          className="fixed right-2 top-2 w-8 h-8 bg-white rounded-full shadow-md pointer flex items-center justify-center"
                        >
                          <X />
                        </label>
                        <SearchNormal />
                        <SideMobileMenu />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <BottomMenu />
          </nav>
        </>
      )}
    </>
  );
};

export default VietnameseHeader;