"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import Link from "next/link";
import {
  Users,
  FileText,
  Volume2, // Thay thế Megaphone
  BarChart, // Thay thế BarChart3
  TrendingUp,
  TrendingDown,
  Activity,
  Calendar,
  Clock,
  Download,
  Eye,
  Heart,
  MessageCircle,
  AlertTriangle,
  CheckCircle,
  Plus,
  Settings
} from "react-feather";

interface DashboardProps {
  departmentId: string;
  department: any;
}

interface DashboardStats {
  members: {
    total: number;
    active: number;
    new: number;
    admins: number;
  };
  files: {
    total: number;
    uploaded: number;
    downloaded: number;
    storage: number;
  };
  announcements: {
    total: number;
    published: number;
    views: number;
    engagement: number;
  };
  activity: {
    logins: number;
    actions: number;
    avgSession: number;
    productivity: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'member_join' | 'file_upload' | 'announcement' | 'comment' | 'like';
  user: {
    name: string;
    avatar?: string;
  };
  description: string;
  timestamp: string;
  metadata?: any;
}

export default function DepartmentDashboard({ departmentId, department }: DashboardProps) {
  const [stats, setStats] = useState<DashboardStats>({
    members: { total: 0, active: 0, new: 0, admins: 0 },
    files: { total: 0, uploaded: 0, downloaded: 0, storage: 0 },
    announcements: { total: 0, published: 0, views: 0, engagement: 0 },
    activity: { logins: 0, actions: 0, avgSession: 0, productivity: 0 }
  });
  
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d'); // 7d, 30d, 90d

  useEffect(() => {
    fetchDashboardData();
  }, [departmentId, timeRange]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockStats: DashboardStats = {
        members: {
          total: 45,
          active: 38,
          new: 5,
          admins: 3
        },
        files: {
          total: 234,
          uploaded: 12,
          downloaded: 89,
          storage: 2.4 // GB
        },
        announcements: {
          total: 18,
          published: 15,
          views: 456,
          engagement: 78
        },
        activity: {
          logins: 156,
          actions: 892,
          avgSession: 45, // minutes
          productivity: 85 // percentage
        }
      };

      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'member_join',
          user: { name: 'Nguyễn Văn A' },
          description: 'đã tham gia phòng ban',
          timestamp: '2 giờ trước'
        },
        {
          id: '2',
          type: 'file_upload',
          user: { name: 'Trần Thị B' },
          description: 'đã tải lên file "Báo cáo tháng 12.pdf"',
          timestamp: '4 giờ trước'
        },
        {
          id: '3',
          type: 'announcement',
          user: { name: 'Lê Văn C' },
          description: 'đã tạo thông báo "Họp team tuần tới"',
          timestamp: '6 giờ trước'
        },
        {
          id: '4',
          type: 'comment',
          user: { name: 'Phạm Thị D' },
          description: 'đã bình luận về thông báo "Cập nhật quy trình"',
          timestamp: '8 giờ trước'
        },
        {
          id: '5',
          type: 'like',
          user: { name: 'Hoàng Văn E' },
          description: 'đã thích thông báo "Chúc mừng năm mới"',
          timestamp: '1 ngày trước'
        }
      ];

      setStats(mockStats);
      setRecentActivity(mockActivity);
      
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast.error("Có lỗi xảy ra khi tải dữ liệu dashboard");
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'member_join':
        return <Users size={16} className="text-green-500" />;
      case 'file_upload':
        return <FileText size={16} className="text-blue-500" />;
      case 'announcement':
        return <Volume2 size={16} className="text-purple-500" />;
      case 'comment':
        return <MessageCircle size={16} className="text-orange-500" />;
      case 'like':
        return <Heart size={16} className="text-red-500" />;
      default:
        return <Activity size={16} className="text-gray-500" />;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatStorage = (gb: number) => {
    if (gb >= 1000) {
      return (gb / 1000).toFixed(1) + ' TB';
    }
    return gb.toFixed(1) + ' GB';
  };

  if (loading) {
    return (
      <div className="w-full p-6 flex items-center justify-center">
        <div className="text-gray-600">Đang tải dashboard...</div>
      </div>
    );
  }

  return (
    <div className="w-full p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard phòng ban</h1>
          <p className="text-gray-600">{department?.name} - {department?.code}</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7d">7 ngày qua</option>
            <option value="30d">30 ngày qua</option>
            <option value="90d">90 ngày qua</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Members Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Thành viên</p>
              <p className="text-2xl font-bold text-gray-900">{stats.members.total}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users size={24} className="text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">{stats.members.active} hoạt động</span>
            </div>
            <div className="flex items-center space-x-1">
              <TrendingUp size={14} className="text-green-500" />
              <span className="text-green-600">+{stats.members.new} mới</span>
            </div>
          </div>
        </div>

        {/* Files Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">File</p>
              <p className="text-2xl font-bold text-gray-900">{stats.files.total}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <FileText size={24} className="text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <span className="text-gray-600">{formatStorage(stats.files.storage)} sử dụng</span>
            </div>
            <div className="flex items-center space-x-1">
              <Download size={14} className="text-blue-500" />
              <span className="text-blue-600">{stats.files.downloaded} tải xuống</span>
            </div>
          </div>
        </div>

        {/* Announcements Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Thông báo</p>
              <p className="text-2xl font-bold text-gray-900">{stats.announcements.total}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Volume2 size={24} className="text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <Eye size={14} className="text-gray-500" />
              <span className="text-gray-600">{formatNumber(stats.announcements.views)} lượt xem</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-green-600">{stats.announcements.engagement}% tương tác</span>
            </div>
          </div>
        </div>

        {/* Activity Stats */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Hoạt động</p>
              <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.activity.actions)}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Activity size={24} className="text-orange-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <Clock size={14} className="text-gray-500" />
              <span className="text-gray-600">{stats.activity.avgSession}p/phiên</span>
            </div>
            <div className="flex items-center space-x-1">
              <BarChart size={14} className="text-green-500" />
              <span className="text-green-600">{stats.activity.productivity}% hiệu suất</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Hoạt động gần đây</h3>
          </div>
          <div className="p-6">
            {recentActivity.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Activity size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Chưa có hoạt động nào</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        <span className="font-medium">{activity.user.name}</span>{' '}
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500">{activity.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Thao tác nhanh</h3>
          </div>
          <div className="p-6 space-y-3">
            <Link
              href={`/dashboard/departments/${departmentId}/members/add`}
              className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users size={16} className="text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Thêm thành viên</p>
                <p className="text-xs text-gray-600">Mời thành viên mới tham gia</p>
              </div>
            </Link>

            <Link
              href={`/dashboard/departments/${departmentId}/announcements/create`}
              className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="p-2 bg-purple-100 rounded-lg">
                <Volume2 size={16} className="text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Tạo thông báo</p>
                <p className="text-xs text-gray-600">Gửi thông báo đến thành viên</p>
              </div>
            </Link>

            <Link
              href={`/dashboard/departments/${departmentId}/files`}
              className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="p-2 bg-green-100 rounded-lg">
                <FileText size={16} className="text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Quản lý file</p>
                <p className="text-xs text-gray-600">Tải lên và chia sẻ file</p>
              </div>
            </Link>

            <Link
              href={`/dashboard/departments/${departmentId}/reports/create`}
              className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="p-2 bg-orange-100 rounded-lg">
                <BarChart size={16} className="text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Tạo báo cáo</p>
                <p className="text-xs text-gray-600">Tạo báo cáo thống kê</p>
              </div>
            </Link>

            <Link
              href={`/dashboard/departments/${departmentId}/settings`}
              className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="p-2 bg-gray-100 rounded-lg">
                <Settings size={16} className="text-gray-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Cài đặt</p>
                <p className="text-xs text-gray-600">Cấu hình phòng ban</p>
              </div>
            </Link>
          </div>
        </div>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Member Growth Chart */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Tăng trưởng thành viên</h3>
          </div>
          <div className="p-6">
            <div className="h-64 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <BarChart size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Biểu đồ tăng trưởng thành viên</p>
                <p className="text-sm">(Sẽ được implement với chart library)</p>
              </div>
            </div>
          </div>
        </div>

        {/* Activity Trends */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Xu hướng hoạt động</h3>
          </div>
          <div className="p-6">
            <div className="h-64 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <TrendingUp size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Biểu đồ xu hướng hoạt động</p>
                <p className="text-sm">(Sẽ được implement với chart library)</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
