@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and override any external CSS */
* {
  box-sizing: border-box;
}

/* Override any external layout.css */
body, html {
  margin: 0 !important;
  padding: 0 !important;
  font-family: system-ui, -apple-system, BlinkMacSystemFont !important;
  /* Removed forced color to allow inheritance */
}

/* Allow color inheritance instead of forcing - DISABLED */
/* *, *::before, *::after {
  color: inherit !important;
} */

/* Default text color for elements without specific colors - DISABLED */
/* p, span, div, h1, h2, h3, h4, h5, h6, label, input, textarea, select, button, li, td, th, strong, em, small {
  color: inherit !important;
} */

/* Override any problematic text colors */
/* Temporarily disable gray color overrides
.text-gray-400, .text-gray-300, .text-gray-200, .text-gray-100 {
  color: #1f2937 !important;
}
*/

/* White text ONLY for specific cases that need it */
.text-white,
.btn-primary,
.bg-blue-600,
.bg-green-600,
.bg-red-600,
.bg-yellow-600,
.bg-purple-600,
.bg-gray-600,
.bg-black {
  color: #ffffff !important;
}

/* Buttons with colored backgrounds should have white text */
button.bg-blue-600,
button.bg-green-600,
button.bg-red-600,
button.bg-yellow-600,
button.bg-purple-600,
button.bg-gray-600,
.btn-primary,
.btn-success,
.btn-danger,
.btn-warning {
  color: #ffffff !important;
}

/* Form elements */
/* Temporarily disable form color overrides
input, textarea, select {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}
*/

/* Links */
a {
  color: #3b82f6 !important;
}

a:hover {
  color: #2563eb !important;
}

/* Ensure dropdown and modal content is visible */
/* Temporarily disable dropdown color overrides
.dropdown-content, .modal-content, .card {
  color: #1f2937 !important;
}
*/

/* DaisyUI overrides */
.menu li a, .menu li summary {
  color: #1f2937 !important;
}

/* FORCE COLOR CLASSES TO WORK - Override inheritance */
.text-red-600, 
.text-red-600 *, 
div.text-red-600, 
div.text-red-600 *,
span.text-red-600,
span.text-red-600 * {
  color: #dc2626 !important;
  -webkit-text-fill-color: #dc2626 !important;
}

.text-blue-400, 
.text-blue-400 *, 
div.text-blue-400, 
div.text-blue-400 *,
span.text-blue-400,
span.text-blue-400 * {
  color: #60a5fa !important;
  -webkit-text-fill-color: #60a5fa !important;
}

.text-white, 
.text-white *, 
div.text-white, 
div.text-white *,
span.text-white,
span.text-white * {
  color: #ffffff !important;
  -webkit-text-fill-color: #ffffff !important;
}
@font-face {
  font-display: swap;
}
:root {
  --primary: #923A3C;
}
html, body {
  touch-action: manipulation;
}
.content-wrapper a {
  color: var(--primary);
}
.content-wrapper img{
  margin: 10px 0px;
}
body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont;
  font-size: 1rem;
}
.container {
  max-width: 1280px;
}
.main-section{
  max-width: 1130px;
}
.menu li  summary:hover,
.menu li a:hover{
  border-radius: 5px;
  color: var(--primary);
  background: none;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  display: none;
}
.list-post:before {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  content: "";
  background: #ababab;
  position: absolute;
  top: 8px;
  left: -15px;
}
.sidebar .short-text {
  display: none;
}
/* Global Styles */
.is-active {
  background: black;
  color: #fff;
}

/* Table Styles - Ensure text is visible */
/* Temporarily disable table color overrides
table {
  color: #1f2937 !important; 
}

table th {
  color: #1f2937 !important;
  font-weight: 600 !important;
}
*/

/* table td {
  color: #111827 !important;
} */

table tbody tr:hover {
  background-color: #f9fafb !important; /* gray-50 */
}

/* Ensure buttons and links in tables are visible */
table button {
  color: #3b82f6 !important; /* blue-500 */
}

table a {
  color: #3b82f6 !important; /* blue-500 */
  text-decoration: underline;
}


.first-block{
  background: #fcfaf6;
}


.app {
  height: 100%;
}

.treeRoot {
  box-sizing: border-box;
  height: 100%;
  padding: 32px;
}

.draggingSource {
  opacity: 0.3;
}

.placeholderContainer {
  position: relative;
}



/* Remove default bullets */
/* Remove bullets from task list */
ul[data-type="taskList"] {
  list-style-type: none;
  padding: 0;
}

/* Ensure task items are inline */
[data-type="taskList"] li {
  display: flex;
}

/* Style the checkbox */
[data-type="taskList"] li > label {
  margin-top: .25rem;
  margin-right: .5rem;
  flex: 1 1 auto;
  flex-shrink: 0;
  flex-grow: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

/* Ensure checkbox is properly sized */



/* Add to global CSS if needed */
.video-player {
  width: 100%;
  height: auto;
  aspect-ratio: 16 / 9;
}

/* Override user agent stylesheet for ::marker */
::marker {
  unicode-bidi: normal !important;
  font-variant-numeric: normal !important;
  text-transform: inherit !important;
  text-indent: inherit !important;
  text-align: inherit !important;
  text-align-last: inherit !important;
  content: none !important;
}

/* Remove list markers completely */
ul, ol {
  list-style: none !important;
}

ul::marker, ol::marker, li::marker {
  content: none !important;
  display: none !important;
}

/* Custom list styles if needed */
.custom-list {
  list-style: disc;
  padding-left: 1.5rem;
}

.custom-list-ordered {
  list-style: decimal;
  padding-left: 1.5rem;
}



.video-player {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  background: #000 !important;
  display: block !important;
  margin: 1rem 0 !important;
}

/* FINAL OVERRIDE - ALLOW COLOR INHERITANCE */
/* This should be at the end to override everything else */
/* DISABLED - Causing color inheritance issues */
/* body *,
body *::before,
body *::after,
#__next *,
#__next *::before,
#__next *::after {
  color: inherit !important;
} */

/* Exceptions for elements that should have white text */
.text-white,
.btn-primary,
.bg-blue-600 *,
.bg-green-600 *,
.bg-red-600 *,
.bg-yellow-600 *,
.bg-purple-600 *,
.bg-gray-600 *,
.bg-gray-800 *,
.bg-gray-900 *,
.bg-black *,
button.bg-blue-600,
button.bg-green-600,
button.bg-red-600,
button.bg-yellow-600,
button.bg-purple-600,
button.bg-gray-600 {
  color: #ffffff !important;
}

/* Ensure icons inherit proper colors - DISABLED */
/* svg {
  color: inherit !important;
} */

/* Modal and Dialog specific overrides */
/* Temporarily disable modal color overrides
.modal, .modal-box, .dialog, [role="dialog"] {
  color: #1f2937 !important;
}

.modal *, .modal-box *, .dialog *, [role="dialog"] * {
  color: #1f2937 !important;
}

Fixed positioned elements (modals, dropdowns, tooltips)
.fixed, .absolute, .relative {
  color: #1f2937 !important;
}

.fixed *, .absolute *, .relative * {
  color: #1f2937 !important;
}

Z-index high elements (likely modals/overlays)
.z-50, .z-40, .z-30 {
  color: #1f2937 !important;
}

.z-50 *, .z-40 *, .z-30 * {
  color: #1f2937 !important;
}

Specific modal classes
.modal-content, .popup, .overlay, .dropdown-menu {
  color: #1f2937 !important;
}
*/

/* Removed conflicting modal color override */

/* Security: Disable image drag and protect sensitive content in production */
@media (min-width: 1px) {
  .security-disabled {
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    pointer-events: auto;
  }

  /* Only disable drag for images and media */
  .security-disabled img,
  .security-disabled video {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
    pointer-events: auto;
  }

  /* Allow text selection for better UX */
  .security-disabled p,
  .security-disabled h1,
  .security-disabled h2,
  .security-disabled h3,
  .security-disabled h4,
  .security-disabled h5,
  .security-disabled h6,
  .security-disabled span,
  .security-disabled div {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}
