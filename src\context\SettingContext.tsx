"use client";
import { createContext, useContext, useEffect, useState } from "react";
import settingApiRequest from "@/apiRequests/common";
import { usePathname } from "next/navigation";
import { clearSettingCache, setCacheWithTimestamp, getCacheIfFresh } from "@/utils/cacheUtils";

interface Setting {
  title: string;
  desc: string;
  address: string;
  email: string;
  hotline: string;
  contact: string;
  copyright: string;
  footerBLock1: string;
  footerBLock2: string;
  logo?: {
    _id: string;
    path: string;
    folder: string;
  };
  ads1?: string;
  openReg?: boolean;
}

interface Menu {
  title: string;
  position: string;
  obj: object;
}

interface SettingContextType {
  setting: Setting | null;
  loading: boolean;
  menus: Menu | null;
  refreshSettings: () => void;
}

const SettingContext = createContext<SettingContextType | undefined>(undefined);

export const SettingProvider = ({ children }: { children: React.ReactNode }) => {
  const [setting, setSetting] = useState<Setting | null>(null);
  const [menus, setMenus] = useState<Menu | null>(null);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname(); // Get current route

  const fetchSetting = async (forceRefresh = false) => {
    try {
      setLoading(true);

      // Check cache first (unless force refresh)
      if (!forceRefresh) {
        const cachedSetting = getCacheIfFresh("siteSetting", 5); // 5 minutes cache
        const cachedMenus = getCacheIfFresh("siteMenus", 5);

        if (cachedSetting && cachedMenus) {
          setSetting(cachedSetting);
          setMenus(cachedMenus);
          setLoading(false);
          return;
        }
      }

      const resSetting = await settingApiRequest.commonFetchSetting();
      if (resSetting.payload.success) {
        setSetting(resSetting.payload.setting);
        setMenus(resSetting.payload.menus);

        // Store settings with timestamp
        setCacheWithTimestamp("siteSetting", resSetting.payload.setting);
        setCacheWithTimestamp("siteMenus", resSetting.payload.menus);
      } else {
        console.error("Failed to fetch settings");
      }
    } catch (error) {
      console.error("Error fetching settings:", error);
    } finally {
      setLoading(false);
    }
  };

  const refreshSettings = () => {
    console.log("🔄 Force refreshing settings...");
    clearSettingCache();
    fetchSetting(true);
  };

  useEffect(() => {
    // Always fetch settings if on homepage OR dashboard routes
    const shouldFetch = pathname === "/" || pathname.startsWith("/dashboard");

    if (shouldFetch) {
      fetchSetting();
    } else {
      // Try to load from cache for other routes
      const cachedSetting = getCacheIfFresh("siteSetting", 30);
      const cachedMenus = getCacheIfFresh("siteMenus", 30);

      if (cachedSetting && cachedMenus) {
        setSetting(cachedSetting);
        setMenus(cachedMenus);
        setLoading(false);
      } else {
        fetchSetting();
      }
    }
  }, [pathname]); // Fetch on route change

  return (
    <SettingContext.Provider value={{ setting, loading, menus, refreshSettings }}>
      {children}
    </SettingContext.Provider>
  );
};

export const useSetting = () => {
  const context = useContext(SettingContext);
  if (!context) {
    throw new Error("useSetting must be used within a SettingProvider");
  }
  return context;
};
