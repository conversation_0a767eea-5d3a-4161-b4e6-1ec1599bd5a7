const express = require("express");
const router = express.Router();
const passport = require("passport");
const departmentController = require("../controllers/department");
const departmentReportController = require("../controllers/departmentReport");
const departmentAnnouncementController = require("../controllers/departmentAnnouncement");
const checkBlacklistedToken = require("../middleware/checkBlacklistedToken");
const { mongoIdValidation } = require("../middleware/inputValidation");

// Middleware to ensure user is authenticated
const requireAuth = passport.authenticate('user', { session: false });

// Get all departments (system admin only)
router.post("/", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.getAllDepartments
);

// Get single department
router.get("/:id", 
  requireAuth, 
  checkBlacklistedToken, 
  mongoIdValidation,
  departmentController.getDepartment
);

// Create department (system admin only)
router.post("/create", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.createDepartment
);

// Update department (system admin only)
router.put("/:id", 
  requireAuth, 
  checkBlacklistedToken, 
  mongoIdValidation,
  departmentController.updateDepartment
);

// Assign department admin (system admin only)
router.post("/assign-admin", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.assignDepartmentAdmin
);

// Get department members
router.get("/:id/members",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.getDepartmentMembers
);

// Search available users for department
router.get("/:id/available-users",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.getAvailableUsers
);

// Add members to department
router.post("/:id/add-members",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.addMembers
);

// Remove member from department
router.delete("/:id/members/:userId",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.removeMember
);

// Create new member for department
router.post("/:id/create-member",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.createMember
);

// Delete department (soft delete)
router.delete("/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.deleteDepartment
);

// Restore deleted department
router.post("/:id/restore",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.restoreDepartment
);

// Department Reports Routes
// Get all reports for a department
router.get("/:departmentId/reports",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.getDepartmentReports
);

// Create new report for department
router.post("/:departmentId/reports",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.createReport
);

// Get single report
router.get("/reports/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.getReport
);

// Update report
router.put("/reports/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.updateReport
);

// Delete report
router.delete("/reports/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.deleteReport
);

// Add comment to report
router.post("/reports/:id/comments",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.addComment
);

// Department Announcements Routes
// Get all announcements for a department
router.get("/:departmentId/announcements",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.getDepartmentAnnouncements
);

// Create new announcement for department
router.post("/:departmentId/announcements",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.createAnnouncement
);

// Get single announcement
router.get("/announcements/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.getAnnouncement
);

// Update announcement
router.put("/announcements/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.updateAnnouncement
);

// Delete announcement
router.delete("/announcements/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.deleteAnnouncement
);

module.exports = router;
