const express = require("express");
const router = express.Router();
const passport = require("passport");
const departmentController = require("../controllers/department");
const departmentReportController = require("../controllers/departmentReport");
const departmentAnnouncementController = require("../controllers/departmentAnnouncement");
const departmentFileController = require("../controllers/departmentFile");
const departmentImportExportController = require("../controllers/departmentImportExport");
const checkBlacklistedToken = require("../middleware/checkBlacklistedToken");
const { mongoIdValidation } = require("../middleware/inputValidation");

// Middleware to ensure user is authenticated
const requireAuth = passport.authenticate('user', { session: false });

// Get all departments (system admin only)
router.post("/", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.getAllDepartments
);

// Get single department
router.get("/:id", 
  requireAuth, 
  checkBlacklistedToken, 
  mongoIdValidation,
  departmentController.getDepartment
);

// Create department (system admin only)
router.post("/create", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.createDepartment
);

// Update department (system admin only)
router.put("/:id", 
  requireAuth, 
  checkBlacklistedToken, 
  mongoIdValidation,
  departmentController.updateDepartment
);

// Assign department admin (system admin only)
router.post("/assign-admin", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.assignDepartmentAdmin
);

// Get department members
router.get("/:id/members",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.getDepartmentMembers
);

// Search available users for department
router.get("/:id/available-users",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.getAvailableUsers
);

// Add members to department
router.post("/:id/add-members",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.addMembers
);

// Remove member from department
router.delete("/:id/members/:userId",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.removeMember
);

// Create new member for department
router.post("/:id/create-member",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.createMember
);

// Delete department (soft delete)
router.delete("/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.deleteDepartment
);

// Restore deleted department
router.post("/:id/restore",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.restoreDepartment
);

// Department Reports Routes
// Get all reports for a department
router.get("/:departmentId/reports",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.getDepartmentReports
);

// Create new report for department
router.post("/:departmentId/reports",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.createReport
);

// Get single report
router.get("/reports/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.getReport
);

// Update report
router.put("/reports/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.updateReport
);

// Delete report
router.delete("/reports/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.deleteReport
);

// Add comment to report
router.post("/reports/:id/comments",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentReportController.addComment
);

// Department Announcements Routes
// Get all announcements for a department
router.get("/:departmentId/announcements",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.getDepartmentAnnouncements
);

// Create new announcement for department
router.post("/:departmentId/announcements",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.createAnnouncement
);

// Get single announcement
router.get("/announcements/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.getAnnouncement
);

// Update announcement
router.put("/announcements/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.updateAnnouncement
);

// Delete announcement
router.delete("/announcements/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentAnnouncementController.deleteAnnouncement
);

// Department Files Routes
// Get all files for a department
router.get("/:departmentId/files",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentFileController.getDepartmentFiles
);

// Upload file to department
router.post("/:departmentId/files/upload",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentFileController.upload.single('file'),
  departmentFileController.uploadFile
);

// Get single file
router.get("/files/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentFileController.getFile
);

// Download file
router.get("/files/:id/download",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentFileController.downloadFile
);

// Update file metadata
router.put("/files/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentFileController.updateFile
);

// Delete file
router.delete("/files/:id",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentFileController.deleteFile
);

// Share file
router.post("/files/:id/share",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentFileController.shareFile
);

// Import/Export Routes
// Export department members
router.get("/:departmentId/export/members",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentImportExportController.exportMembers
);

// Export department reports
router.get("/:departmentId/export/reports",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentImportExportController.exportReports
);

// Export comprehensive department data
router.get("/:departmentId/export/data",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentImportExportController.exportDepartmentData
);

module.exports = router;
