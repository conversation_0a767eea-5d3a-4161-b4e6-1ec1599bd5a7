const express = require("express");
const router = express.Router();
const passport = require("passport");
const departmentController = require("../controllers/department");
const checkBlacklistedToken = require("../middleware/checkBlacklistedToken");
const { mongoIdValidation } = require("../middleware/inputValidation");

// Middleware to ensure user is authenticated
const requireAuth = passport.authenticate('user', { session: false });

// Get all departments (system admin only)
router.post("/", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.getAllDepartments
);

// Get single department
router.get("/:id", 
  requireAuth, 
  checkBlacklistedToken, 
  mongoIdValidation,
  departmentController.getDepartment
);

// Create department (system admin only)
router.post("/create", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.createDepartment
);

// Update department (system admin only)
router.put("/:id", 
  requireAuth, 
  checkBlacklistedToken, 
  mongoIdValidation,
  departmentController.updateDepartment
);

// Assign department admin (system admin only)
router.post("/assign-admin", 
  requireAuth, 
  checkBlacklistedToken, 
  departmentController.assignDepartmentAdmin
);

// Get department members
router.get("/:id/members",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.getDepartmentMembers
);

// Search available users for department
router.get("/:id/available-users",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.getAvailableUsers
);

// Add members to department
router.post("/:id/add-members",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.addMembers
);

// Remove member from department
router.delete("/:id/members/:userId",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.removeMember
);

// Create new member for department
router.post("/:id/create-member",
  requireAuth,
  checkBlacklistedToken,
  mongoIdValidation,
  departmentController.createMember
);

module.exports = router;
