const verifyPermission = (requiredPermission) => {
  return function(req, res, next) {
    const user = req.user;
    
    console.log('[DEBUG] verifyPermission called for permission:', requiredPermission);
    console.log('[DEBUG] User:', user ? {
      id: user._id,
      email: user.email,
      systemRole: user.systemRole,
      isDepartmentAdmin: user.isDepartmentAdmin,
      department: user.department,
      permissions: user.permissions
    } : 'No user');
    
    if (!user) {
      const err = new Error('Authentication required');
      err.status = 401;
      return next(err);
    }

    // System admin always has all permissions
    if (user.systemRole === 'admin') {
      console.log('[DEBUG] User is system admin, allowing access');
      return next();
    }

    // Check if user has the specific permission
    if (user.permissions && user.permissions.includes(requiredPermission)) {
      console.log('[DEBUG] User has required permission, allowing access');
      return next();
    }

    // For department-specific permissions, check if user is department admin
    if (requiredPermission.startsWith('dept_') && user.isDepartmentAdmin && user.department) {
      console.log('[DEBUG] User is department admin, allowing department access');
      return next();
    }

    // User doesn't have required permission
    console.log('[DEBUG] User does not have required permission, denying access');
    console.log('[DEBUG] Required permission:', requiredPermission);
    console.log('[DEBUG] User permissions:', user.permissions);
    const err = new Error('You are not authorized to perform this operation!');
    err.status = 403;
    return next(err);
  };
};

const verifyAnyPermission = (requiredPermissions) => {
  return function(req, res, next) {
    const user = req.user;
    
    if (!user) {
      const err = new Error('Authentication required');
      err.status = 401;
      return next(err);
    }

    // System admin always has all permissions
    if (user.systemRole === 'admin') {
      return next();
    }

    // Check if user has any of the required permissions
    if (user.permissions && requiredPermissions.some(permission => user.permissions.includes(permission))) {
      return next();
    }

    // User doesn't have any required permission
    const err = new Error('You are not authorized to perform this operation!');
    err.status = 403;
    return next(err);
  };
};

const verifyAllPermissions = (requiredPermissions) => {
  return function(req, res, next) {
    const user = req.user;
    
    if (!user) {
      const err = new Error('Authentication required');
      err.status = 401;
      return next(err);
    }

    // System admin always has all permissions
    if (user.systemRole === 'admin') {
      return next();
    }

    // Check if user has all required permissions
    if (user.permissions && requiredPermissions.every(permission => user.permissions.includes(permission))) {
      return next();
    }

    // User doesn't have all required permissions
    const err = new Error('You are not authorized to perform this operation!');
    err.status = 403;
    return next(err);
  };
};

// For dashboard stats - requires any dashboard-related permission
const verifyDashboardAccess = verifyAnyPermission([
  'user_view',
  'file_view',
  'system_settings_view',
  'analytics_view',
  'dept_analytics_view'
]);



// Legacy file management (for backward compatibility)
const verifyFileView = verifyPermission('file_view');
const verifyFileUpload = verifyPermission('file_upload');
const verifyFileDelete = verifyPermission('file_delete');

// System settings (system admin only)
const verifySystemSettingsView = verifyPermission('system_settings_view');
const verifySystemSettingsEdit = verifyPermission('system_settings_edit');



const verifyDeptFileView = verifyPermission('dept_file_view');
const verifyDeptFileUpload = verifyPermission('dept_file_upload');
const verifyDeptFileDelete = verifyPermission('dept_file_delete');

const verifyDeptAnalytics = verifyPermission('dept_analytics_view');

// System admin only permissions
const verifySystemAdmin = (req, res, next) => {
  const user = req.user;

  if (!user) {
    const err = new Error('Authentication required');
    err.status = 401;
    return next(err);
  }

  if (user.systemRole !== 'admin') {
    const err = new Error('System administrator access required');
    err.status = 403;
    return next(err);
  }

  return next();
};

// Department admin permissions
const verifyDepartmentAdmin = (req, res, next) => {
  const user = req.user;

  if (!user) {
    const err = new Error('Authentication required');
    err.status = 401;
    return next(err);
  }

  if (user.systemRole === 'admin') {
    return next(); // System admin can access everything
  }

  if (!user.isDepartmentAdmin || !user.department) {
    const err = new Error('Department administrator access required');
    err.status = 403;
    return next(err);
  }

  return next();
};

module.exports = {
  verifyPermission,
  verifyAnyPermission,
  verifyAllPermissions,
  verifyDashboardAccess,

  // File permissions
  verifyFileView,
  verifyFileUpload,
  verifyFileDelete,
  verifySystemSettingsView,
  verifySystemSettingsEdit,

  // Department file permissions
  verifyDeptFileView,
  verifyDeptFileUpload,
  verifyDeptFileDelete,
  verifyDeptAnalytics,

  // Role-based permissions
  verifySystemAdmin,
  verifyDepartmentAdmin
};
