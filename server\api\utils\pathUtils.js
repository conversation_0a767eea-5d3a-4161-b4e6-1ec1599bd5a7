const path = require('path');

/**
 * Normalize file path to use forward slashes for web URLs
 * Converts Windows backslashes to forward slashes
 * @param {string} filePath - The file path to normalize
 * @returns {string} - Normalized path with forward slashes
 */
const normalizeWebPath = (filePath) => {
  if (!filePath) return '';
  
  // Replace all backslashes with forward slashes
  return filePath.replace(/\\/g, '/');
};

/**
 * Ensure path starts with forward slash for absolute URLs
 * @param {string} filePath - The file path
 * @returns {string} - Path with leading slash
 */
const ensureAbsolutePath = (filePath) => {
  if (!filePath) return '';
  
  const normalized = normalizeWebPath(filePath);
  return normalized.startsWith('/') ? normalized : `/${normalized}`;
};

/**
 * Create a web-safe URL from file path
 * @param {string} filePath - The file path
 * @param {string} baseUrl - Optional base URL (default: empty)
 * @returns {string} - Web-safe URL
 */
const createWebUrl = (filePath, baseUrl = '') => {
  if (!filePath) return '';
  
  const normalized = normalizeWebPath(filePath);
  const absolute = ensureAbsolutePath(normalized);
  
  return baseUrl ? `${baseUrl.replace(/\/$/, '')}${absolute}` : absolute;
};

/**
 * Extract filename from path
 * @param {string} filePath - The file path
 * @returns {string} - Just the filename
 */
const getFilename = (filePath) => {
  if (!filePath) return '';
  
  return path.basename(filePath);
};

/**
 * Get file extension
 * @param {string} filePath - The file path
 * @returns {string} - File extension (with dot)
 */
const getFileExtension = (filePath) => {
  if (!filePath) return '';
  
  return path.extname(filePath);
};

/**
 * Validate if path is safe (no directory traversal)
 * @param {string} filePath - The file path to validate
 * @returns {boolean} - True if path is safe
 */
const isSafePath = (filePath) => {
  if (!filePath) return false;
  
  // Check for directory traversal attempts
  const normalized = path.normalize(filePath);
  return !normalized.includes('..') && !normalized.startsWith('/');
};

module.exports = {
  normalizeWebPath,
  ensureAbsolutePath,
  createWebUrl,
  getFilename,
  getFileExtension,
  isSafePath
};
