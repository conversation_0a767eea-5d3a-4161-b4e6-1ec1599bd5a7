const mongoose = require('mongoose');
require('dotenv').config();

// Import models
require('../api/models/user');
require('../api/models/file');
require('../api/models/setting');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

const File = mongoose.model('File');
const Setting = mongoose.model('Setting');

// Fix file URLs
async function fixFileUrls() {
  try {
    console.log('🔄 Starting URL fix...');
    
    // Fix File model URLs
    const files = await File.find({});
    console.log(`📊 Found ${files.length} files to check`);
    
    let fixedFiles = 0;
    for (const file of files) {
      if (file.url && file.url.includes('/server/uploads/')) {
        const newUrl = file.url.replace('/server/uploads/', '/uploads/');
        console.log(`📝 Fixing: ${file.url} → ${newUrl}`);
        file.url = newUrl;
        await file.save();
        fixedFiles++;
      }
    }
    
    console.log(`✅ Fixed ${fixedFiles} file URLs`);
    
    // Fix Setting model logo paths
    const settings = await Setting.find({});
    console.log(`📊 Found ${settings.length} settings to check`);

    let fixedSettings = 0;
    for (const setting of settings) {
      if (setting.logo && setting.logo.path) {
        let newPath = setting.logo.path;
        let needsUpdate = false;

        // Fix /server/uploads/ → /uploads/
        if (newPath.includes('/server/uploads/')) {
          newPath = newPath.replace('/server/uploads/', '/uploads/');
          needsUpdate = true;
        }

        // Fix server/uploads/ → /uploads/ (without leading slash)
        if (newPath.includes('server/uploads/') && !newPath.startsWith('/')) {
          newPath = newPath.replace('server/uploads/', '/uploads/');
          needsUpdate = true;
        }

        if (needsUpdate) {
          console.log(`📝 Fixing logo: ${setting.logo.path} → ${newPath}`);
          setting.logo.path = newPath;
          setting.markModified('logo');
          await setting.save();
          fixedSettings++;
        }
      }
    }
    
    console.log(`✅ Fixed ${fixedSettings} setting logo paths`);
    console.log('✅ URL fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing URLs:', error);
    throw error;
  }
}

// Run fix
async function runFix() {
  try {
    console.log('🚀 Starting URL fix script...');
    await fixFileUrls();
    console.log('✅ Fix completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  }
}

// Handle MongoDB connection
mongoose.connection.on('connected', () => {
  console.log('📊 Connected to MongoDB');
  runFix();
});

mongoose.connection.on('error', (err) => {
  console.error('❌ MongoDB connection error:', err);
  process.exit(1);
});

mongoose.connection.on('disconnected', () => {
  console.log('📊 Disconnected from MongoDB');
});
