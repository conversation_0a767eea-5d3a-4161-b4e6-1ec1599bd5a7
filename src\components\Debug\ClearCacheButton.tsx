"use client";

import { clearSettingCache, forceRefreshSettings } from "@/utils/cacheUtils";
import { useSetting } from "@/context/SettingContext";

const ClearCacheButton = () => {
  const { refreshSettings } = useSetting();

  const handleClearCache = () => {
    clearSettingCache();
    refreshSettings();
    console.log('🔄 Cache cleared and settings refreshed');
  };

  const handleForceRefresh = () => {
    forceRefreshSettings();
  };

  return (
    <div className="flex gap-2 mb-4">
      <button
        onClick={handleClearCache}
        className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
      >
        🗑️ Clear Cache
      </button>
      <button
        onClick={handleForceRefresh}
        className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
      >
        🔄 Force Refresh
      </button>
    </div>
  );
};

export default ClearCacheButton;
