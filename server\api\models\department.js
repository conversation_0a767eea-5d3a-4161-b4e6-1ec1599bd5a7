const mongoose = require("mongoose");
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const { Schema } = mongoose;

const departmentSchema = new Schema(
  {
    name: { 
      type: String, 
      required: true, 
      unique: true,
      trim: true,
      maxlength: 100
    },
    description: { 
      type: String, 
      default: "",
      trim: true,
      maxlength: 500
    },
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
      maxlength: 10
    },
    defaultPermissions: [
      {
        type: String,
        enum: [
          // System-wide permissions (for system admin departments)
          "system_admin_full_access",
          "system_departments_manage",
          "system_users_manage",
          "system_settings_manage",

          // File Management (department files)
          "dept_file_view",
          "dept_file_upload",
          "dept_file_delete",

          // Department Settings
          "dept_settings_view",
          "dept_settings_edit",

          // Department Reports
          "dept_reports_view",
          "dept_reports_create",
          "dept_reports_export",

          // Department Communication
          "dept_announcements_view",
          "dept_announcements_create",
          "dept_announcements_edit",
          "dept_announcements_delete",

          // Department Posts
          "dept_posts_view",
          "dept_posts_create",
          "dept_posts_edit",
          "dept_posts_delete",

          // Court Case Management
          "court_case_view",
          "court_case_edit",
          "court_case_export",
          "court_case_detailed_stats_view",
          "court_case_import",
          "court_case_delete",
          "court_case_create",
          "court_case_stats_view",

          // News Management
          "news_view",
          "news_edit",
          "news_publish",
          "news_featured",
          "news_delete",
          "news_create",

          // General permissions
          "file_view",
          "file_upload",
          "file_delete",
          "system_settings_view",
          "system_settings_edit",
          "permissions_manage",

          // Additional common permissions
          "posts_view",
          "posts_create", 
          "posts_edit",
          "posts_delete",
          "posts_publish",
          "categories_view",
          "categories_manage",
          "users_view",
          "users_manage"
        ]
      }
    ],
    isActive: { 
      type: Boolean, 
      default: true 
    },
    departmentAdmin: { 
      type: Schema.Types.ObjectId, 
      ref: "User",
      default: null
    },
    maxMembers: {
      type: Number,
      default: 100,
      min: 1
    },
    parentDepartment: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      default: null
    },
    level: {
      type: Number,
      default: 1,
      min: 1,
      max: 5
    }
  },
  { timestamps: true }
);

// Indexes for better performance
departmentSchema.index({ name: 1 });
departmentSchema.index({ code: 1 });
departmentSchema.index({ isActive: 1 });
departmentSchema.index({ departmentAdmin: 1 });
departmentSchema.index({ parentDepartment: 1 });

// Plugin for CASL permissions
departmentSchema.plugin(accessibleRecordsPlugin);

// Virtual for member count
departmentSchema.virtual('memberCount', {
  ref: 'User',
  localField: '_id',
  foreignField: 'department',
  count: true
});

// Virtual for sub-departments
departmentSchema.virtual('subDepartments', {
  ref: 'Department',
  localField: '_id',
  foreignField: 'parentDepartment'
});

// Pre-save middleware
departmentSchema.pre('save', function(next) {
  // Auto-generate code if not provided
  if (!this.code && this.name) {
    this.code = this.name.toUpperCase().replace(/\s+/g, '').substring(0, 10);
  }
  next();
});

// Static methods
departmentSchema.statics.findActiveWithMembers = function() {
  return this.find({ isActive: true })
    .populate('departmentAdmin', 'username email')
    .populate('memberCount')
    .sort({ name: 1 });
};

departmentSchema.statics.findByAdmin = function(adminId) {
  return this.findOne({ 
    departmentAdmin: adminId, 
    isActive: true 
  });
};

// Instance methods
departmentSchema.methods.addDefaultPermissionsToUser = function(user) {
  // Add department's default permissions to user if not already present
  const newPermissions = this.defaultPermissions.filter(
    permission => !user.permissions.includes(permission)
  );
  user.permissions.push(...newPermissions);
  return user;
};

departmentSchema.methods.canManageUser = function(adminUser, targetUser) {
  // Check if admin can manage the target user
  if (!adminUser.isDepartmentAdmin) return false;
  if (!adminUser.department.equals(this._id)) return false;
  if (!targetUser.department.equals(this._id)) return false;
  return true;
};

// Validation
departmentSchema.path('name').validate(function(value) {
  return value && value.length >= 2;
}, 'Department name must be at least 2 characters long');

departmentSchema.path('code').validate(function(value) {
  return value && /^[A-Z0-9]+$/.test(value);
}, 'Department code must contain only uppercase letters and numbers');

module.exports = mongoose.model("Department", departmentSchema);
