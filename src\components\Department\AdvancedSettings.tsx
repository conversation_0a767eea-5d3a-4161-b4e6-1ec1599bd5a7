"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import {
  Settings,
  Save,
  Plus,
  Trash2,
  Users,
  Shield,
  GitBranch, // Thay thế Workflow
  Clock,
  FileText,
  Bell,
  Database,
  Zap
} from "react-feather";

interface AdvancedSettingsProps {
  departmentId: string;
  department: any;
  onUpdate: (settings: any) => void;
}

interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  assignedTo: string;
  autoApprove: boolean;
  timeLimit: number;
  required: boolean;
}

interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'select' | 'boolean';
  required: boolean;
  options?: string[];
  defaultValue?: any;
}

export default function AdvancedSettings({ departmentId, department, onUpdate }: AdvancedSettingsProps) {
  const [activeTab, setActiveTab] = useState('workflow');
  const [saving, setSaving] = useState(false);
  
  // Workflow settings
  const [workflowEnabled, setWorkflowEnabled] = useState(false);
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]);
  
  // Approval process settings
  const [approvalSettings, setApprovalSettings] = useState({
    requireApprovalForJoin: true,
    requireApprovalForLeave: false,
    requireApprovalForFileUpload: false,
    requireApprovalForAnnouncements: true,
    autoApproveAfterDays: 7,
    approvers: [] as string[]
  });
  
  // Custom fields
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  
  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    digestFrequency: 'daily',
    notifyOnNewMember: true,
    notifyOnFileUpload: true,
    notifyOnAnnouncement: true,
    notifyOnMemberLeave: false
  });
  
  // Security settings
  const [securitySettings, setSecuritySettings] = useState({
    enableTwoFactor: false,
    sessionTimeout: 480, // minutes
    maxLoginAttempts: 5,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false
    },
    ipWhitelist: [] as string[],
    allowedFileTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'png', 'gif'],
    maxFileSize: 100 // MB
  });
  
  // Integration settings
  const [integrationSettings, setIntegrationSettings] = useState({
    enableSlackIntegration: false,
    slackWebhookUrl: '',
    enableTeamsIntegration: false,
    teamsWebhookUrl: '',
    enableEmailIntegration: true,
    emailTemplates: {
      welcome: '',
      announcement: '',
      reminder: ''
    }
  });

  useEffect(() => {
    // Load existing settings from department data
    if (department) {
      // Initialize with existing data or defaults
      setWorkflowEnabled(department.workflowEnabled || false);
      setWorkflowSteps(department.workflowSteps || []);
      setApprovalSettings(department.approvalSettings || approvalSettings);
      setCustomFields(department.customFields || []);
      setNotificationSettings(department.notificationSettings || notificationSettings);
      setSecuritySettings(department.securitySettings || securitySettings);
      setIntegrationSettings(department.integrationSettings || integrationSettings);
    }
  }, [department]);

  const addWorkflowStep = () => {
    const newStep: WorkflowStep = {
      id: Date.now().toString(),
      name: '',
      description: '',
      assignedTo: '',
      autoApprove: false,
      timeLimit: 24,
      required: true
    };
    setWorkflowSteps([...workflowSteps, newStep]);
  };

  const removeWorkflowStep = (id: string) => {
    setWorkflowSteps(workflowSteps.filter(step => step.id !== id));
  };

  const updateWorkflowStep = (id: string, field: string, value: any) => {
    setWorkflowSteps(workflowSteps.map(step => 
      step.id === id ? { ...step, [field]: value } : step
    ));
  };

  const addCustomField = () => {
    const newField: CustomField = {
      id: Date.now().toString(),
      name: '',
      type: 'text',
      required: false,
      options: []
    };
    setCustomFields([...customFields, newField]);
  };

  const removeCustomField = (id: string) => {
    setCustomFields(customFields.filter(field => field.id !== id));
  };

  const updateCustomField = (id: string, field: string, value: any) => {
    setCustomFields(customFields.map(customField => 
      customField.id === id ? { ...customField, [field]: value } : customField
    ));
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      const advancedSettings = {
        workflowEnabled,
        workflowSteps,
        approvalSettings,
        customFields,
        notificationSettings,
        securitySettings,
        integrationSettings
      };

      // Call API to save settings
      await onUpdate(advancedSettings);
      toast.success("Cài đặt nâng cao đã được lưu thành công");
      
    } catch (error) {
      console.error("Error saving advanced settings:", error);
      toast.error("Có lỗi xảy ra khi lưu cài đặt");
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    { id: 'workflow', name: 'Quy trình làm việc', icon: GitBranch },
    { id: 'approval', name: 'Phê duyệt', icon: Shield },
    { id: 'fields', name: 'Trường tùy chỉnh', icon: FileText },
    { id: 'notifications', name: 'Thông báo', icon: Bell },
    { id: 'security', name: 'Bảo mật', icon: Shield },
    { id: 'integrations', name: 'Tích hợp', icon: Zap }
  ];

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Settings size={24} className="text-gray-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Cài đặt nâng cao</h2>
              <p className="text-sm text-gray-600">Cấu hình quy trình và tùy chọn nâng cao cho phòng ban</p>
            </div>
          </div>
          <button
            onClick={handleSave}
            disabled={saving}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <Save size={16} className="mr-2" />
            {saving ? 'Đang lưu...' : 'Lưu cài đặt'}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon size={16} />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {/* Workflow Tab */}
        {activeTab === 'workflow' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Quy trình làm việc</h3>
                <p className="text-sm text-gray-600">Thiết lập quy trình phê duyệt và xử lý công việc</p>
              </div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={workflowEnabled}
                  onChange={(e) => setWorkflowEnabled(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Kích hoạt quy trình</span>
              </label>
            </div>

            {workflowEnabled && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-md font-medium text-gray-900">Các bước quy trình</h4>
                  <button
                    onClick={addWorkflowStep}
                    className="inline-flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Plus size={16} className="mr-2" />
                    Thêm bước
                  </button>
                </div>

                {workflowSteps.map((step, index) => (
                  <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h5 className="font-medium text-gray-900">Bước {index + 1}</h5>
                      <button
                        onClick={() => removeWorkflowStep(step.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Tên bước
                        </label>
                        <input
                          type="text"
                          value={step.name}
                          onChange={(e) => updateWorkflowStep(step.id, 'name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Nhập tên bước"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Thời gian xử lý (giờ)
                        </label>
                        <input
                          type="number"
                          value={step.timeLimit}
                          onChange={(e) => updateWorkflowStep(step.id, 'timeLimit', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="1"
                        />
                      </div>
                      
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Mô tả
                        </label>
                        <textarea
                          value={step.description}
                          onChange={(e) => updateWorkflowStep(step.id, 'description', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={2}
                          placeholder="Mô tả chi tiết bước này"
                        />
                      </div>
                      
                      <div className="md:col-span-2 flex items-center space-x-6">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={step.required}
                            onChange={(e) => updateWorkflowStep(step.id, 'required', e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-700">Bắt buộc</span>
                        </label>
                        
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={step.autoApprove}
                            onChange={(e) => updateWorkflowStep(step.id, 'autoApprove', e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-sm text-gray-700">Tự động phê duyệt</span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}

                {workflowSteps.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <GitBranch size={48} className="mx-auto mb-4 text-gray-300" />
                    <p>Chưa có bước quy trình nào</p>
                    <p className="text-sm">Thêm bước đầu tiên để bắt đầu thiết lập quy trình</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Approval Tab */}
        {activeTab === 'approval' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Cài đặt phê duyệt</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between py-3 border-b border-gray-200">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Yêu cầu phê duyệt khi tham gia</h4>
                    <p className="text-sm text-gray-600">Thành viên mới cần được phê duyệt trước khi tham gia</p>
                  </div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={approvalSettings.requireApprovalForJoin}
                      onChange={(e) => setApprovalSettings({
                        ...approvalSettings,
                        requireApprovalForJoin: e.target.checked
                      })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                </div>

                <div className="flex items-center justify-between py-3 border-b border-gray-200">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Yêu cầu phê duyệt khi rời khỏi</h4>
                    <p className="text-sm text-gray-600">Thành viên cần được phê duyệt trước khi rời khỏi phòng ban</p>
                  </div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={approvalSettings.requireApprovalForLeave}
                      onChange={(e) => setApprovalSettings({
                        ...approvalSettings,
                        requireApprovalForLeave: e.target.checked
                      })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                </div>

                <div className="flex items-center justify-between py-3 border-b border-gray-200">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Yêu cầu phê duyệt khi tải file</h4>
                    <p className="text-sm text-gray-600">File tải lên cần được phê duyệt trước khi hiển thị</p>
                  </div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={approvalSettings.requireApprovalForFileUpload}
                      onChange={(e) => setApprovalSettings({
                        ...approvalSettings,
                        requireApprovalForFileUpload: e.target.checked
                      })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                </div>

                <div className="flex items-center justify-between py-3 border-b border-gray-200">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">Yêu cầu phê duyệt thông báo</h4>
                    <p className="text-sm text-gray-600">Thông báo cần được phê duyệt trước khi xuất bản</p>
                  </div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={approvalSettings.requireApprovalForAnnouncements}
                      onChange={(e) => setApprovalSettings({
                        ...approvalSettings,
                        requireApprovalForAnnouncements: e.target.checked
                      })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </label>
                </div>

                <div className="py-3">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tự động phê duyệt sau (ngày)
                  </label>
                  <input
                    type="number"
                    value={approvalSettings.autoApproveAfterDays}
                    onChange={(e) => setApprovalSettings({
                      ...approvalSettings,
                      autoApproveAfterDays: parseInt(e.target.value)
                    })}
                    className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    min="1"
                    max="30"
                  />
                  <p className="text-sm text-gray-600 mt-1">
                    Yêu cầu sẽ được tự động phê duyệt nếu không có phản hồi trong thời gian này
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Custom Fields Tab */}
        {activeTab === 'fields' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Trường tùy chỉnh</h3>
                <p className="text-sm text-gray-600">Thêm các trường thông tin tùy chỉnh cho thành viên</p>
              </div>
              <button
                onClick={addCustomField}
                className="inline-flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus size={16} className="mr-2" />
                Thêm trường
              </button>
            </div>

            {customFields.map((field) => (
              <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h5 className="font-medium text-gray-900">Trường tùy chỉnh</h5>
                  <button
                    onClick={() => removeCustomField(field.id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tên trường
                    </label>
                    <input
                      type="text"
                      value={field.name}
                      onChange={(e) => updateCustomField(field.id, 'name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Nhập tên trường"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Loại dữ liệu
                    </label>
                    <select
                      value={field.type}
                      onChange={(e) => updateCustomField(field.id, 'type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="text">Văn bản</option>
                      <option value="number">Số</option>
                      <option value="date">Ngày tháng</option>
                      <option value="select">Lựa chọn</option>
                      <option value="boolean">Đúng/Sai</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center space-x-4 pt-6">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={field.required}
                        onChange={(e) => updateCustomField(field.id, 'required', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">Bắt buộc</span>
                    </label>
                  </div>
                </div>

                {field.type === 'select' && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Các lựa chọn (mỗi dòng một lựa chọn)
                    </label>
                    <textarea
                      value={field.options?.join('\n') || ''}
                      onChange={(e) => updateCustomField(field.id, 'options', e.target.value.split('\n').filter(opt => opt.trim()))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                      placeholder="Lựa chọn 1&#10;Lựa chọn 2&#10;Lựa chọn 3"
                    />
                  </div>
                )}
              </div>
            ))}

            {customFields.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FileText size={48} className="mx-auto mb-4 text-gray-300" />
                <p>Chưa có trường tùy chỉnh nào</p>
                <p className="text-sm">Thêm trường đầu tiên để thu thập thông tin bổ sung từ thành viên</p>
              </div>
            )}
          </div>
        )}

        {/* Add other tabs content here... */}
        {activeTab === 'notifications' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Cài đặt thông báo</h3>
            <p className="text-gray-600">Cấu hình các loại thông báo và tần suất gửi</p>
            {/* Notification settings content */}
          </div>
        )}

        {activeTab === 'security' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Cài đặt bảo mật</h3>
            <p className="text-gray-600">Cấu hình các chính sách bảo mật cho phòng ban</p>
            {/* Security settings content */}
          </div>
        )}

        {activeTab === 'integrations' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Tích hợp bên ngoài</h3>
            <p className="text-gray-600">Kết nối với các dịch vụ và ứng dụng khác</p>
            {/* Integration settings content */}
          </div>
        )}
      </div>
    </div>
  );
}
