"use client";
import { usePermissions } from "@/hooks/usePermissions";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface PermissionGuardProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredPermissions?: string[];
  requireAll?: boolean; // true = cần tất cả permissions, false = chỉ cần 1 permission
  fallbackPath?: string;
}

export default function PermissionGuard({
  children,
  requiredPermission,
  requiredPermissions = [],
  requireAll = false,
  fallbackPath = "/dashboard"
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, isAdmin } = usePermissions();
  const router = useRouter();

  useEffect(() => {
    // Admin luôn có quyền truy cập
    if (isAdmin) return;

    let hasAccess = false;

    if (requiredPermission) {
      hasAccess = hasPermission(requiredPermission);
    } else if (requiredPermissions.length > 0) {
      if (requireAll) {
        hasAccess = requiredPermissions.every(permission => hasPermission(permission));
      } else {
        hasAccess = hasAnyPermission(requiredPermissions);
      }
    } else {
      hasAccess = true; // Không yêu cầu permission cụ thể
    }

    if (!hasAccess) {
      router.replace(fallbackPath);
    }
  }, [hasPermission, hasAnyPermission, isAdmin, requiredPermission, requiredPermissions, requireAll, fallbackPath, router]);

  // Admin luôn có quyền
  if (isAdmin) {
    return <>{children}</>;
  }

  // Kiểm tra permission
  let hasAccess = false;

  if (requiredPermission) {
    hasAccess = hasPermission(requiredPermission);
  } else if (requiredPermissions.length > 0) {
    if (requireAll) {
      hasAccess = requiredPermissions.every(permission => hasPermission(permission));
    } else {
      hasAccess = hasAnyPermission(requiredPermissions);
    }
  } else {
    hasAccess = true;
  }

  if (!hasAccess) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Không có quyền truy cập</h1>
          <p className="text-gray-600 mb-4">Bạn không có quyền truy cập vào trang này.</p>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Quay lại
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
