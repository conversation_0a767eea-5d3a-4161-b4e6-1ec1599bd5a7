"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Dashboard<PERSON>ogo from "./Navigation/DashboardLogo";
import ButtonLogout from "./ui/button-logout";
import { useAuth } from "@/hooks/useAuth";
import { usePermissions } from "@/hooks/usePermissions";
import { useState } from "react";
import {
  FileText,
  Users,
  Calendar,
  Settings,
  Folder,
  ChevronDown,
  ChevronRight,
  Globe,
  User,
  Shield,
  Upload,
  Home
} from "react-feather";

export default function SideMenu() {
  const { hasPermission } = useAuth();
  const { hasPermission: hasFeaturePermission, isAdmin } = usePermissions();
  const pathname = usePathname();
  const isAdminRoute = pathname.startsWith("/dashboard");
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);

  const toggleMenu = (href: string) => {
    setExpandedMenus(prev =>
      prev.includes(href)
        ? prev.filter(item => item !== href)
        : [...prev, href]
    );
  };

  const isMenuExpanded = (href: string) => expandedMenus.includes(href);
  const isActiveRoute = (href: string) => pathname === href || pathname.startsWith(href + '/');

  const MenuIcon_Component = ({ icon: Icon, className = "" }: { icon: any, className?: string }) => (
    <Icon size={18} className={className} />
  );
  // Build dynamic menu based on specific permissions
  const adminMenu = [];

  // Department management menu - chỉ dành cho system admin
  if (hasFeaturePermission("system_departments_manage")) {
    adminMenu.push({
      title: "Quản Lý Phòng Ban",
      href: "/dashboard/departments",
      icon: Home,
      children: [
        { title: "Danh Sách Phòng Ban", href: "/dashboard/departments" },
        { title: "Thêm Phòng Ban", href: "/dashboard/departments/add" },
      ],
    });
  }



  // File management menu - system admin hoặc department file permissions
  const filePermissions = [
    { permission: "file_view", title: "Xem File", href: "/dashboard/files" },
    { permission: "file_upload", title: "Upload File", href: "/dashboard/files" },
    { permission: "file_delete", title: "Quản Lý File", href: "/dashboard/files" },
    { permission: "dept_file_view", title: "File Phòng Ban", href: "/dashboard/files" },
    { permission: "dept_file_upload", title: "Upload File", href: "/dashboard/files" },
    { permission: "dept_file_delete", title: "Quản Lý File", href: "/dashboard/files" }
  ];

  const hasAnyFilePermission = filePermissions.some(item => hasFeaturePermission(item.permission));

  if (hasAnyFilePermission) {
    adminMenu.push({
      title: "Quản Lý File",
      href: "/dashboard/files",
      icon: Upload,
      children: [
        { title: "Quản Lý File", href: "/dashboard/files" }
      ],
    });
  }

  // System settings menu - chỉ hiển thị nếu có ít nhất 1 permission liên quan
  if (hasFeaturePermission("system_settings_view") || hasFeaturePermission("system_settings_edit")) {
    adminMenu.push({
      title: "Cài đặt",
      href: "/dashboard/setting",
      icon: Settings,
    });
  }
  
  // Dashboard/Analytics menu - luôn hiển thị "Tổng quan"
  // Người có quyền analytics_view sẽ thấy dashboard với thống kê
  // Người không có quyền sẽ thấy dashboard đơn giản
  adminMenu.push({
    title: "Tổng quan",
    href: "/dashboard",
    icon: Globe,
  });
  
  const generalMenu = [
    { title: "Chỉnh sửa thông tin", href: "/dashboard/account", icon: User },
  ];

  const userMenu: any[] = [];

  // Build Manager Menu dynamically based on permissions
  const ManagerMenu = [];
  
  // Only add Settings to Manager menu if they have the permission
  if (hasFeaturePermission("system_settings_view") || hasFeaturePermission("system_settings_edit")) {
    ManagerMenu.push({
      title: "Cài đặt",
      href: "/dashboard/manager/setting",
      icon: Settings,
    });
  }
  
  // Only add File Management if they have the permission
  if (hasFeaturePermission("file_view") || hasFeaturePermission("file_upload") || hasFeaturePermission("file_delete")) {
    ManagerMenu.push({
      title: "Quản Lý File",
      href: "/dashboard/files",
      icon: Upload,
    });
  }
  const MenuItem = ({ item, isChild = false }: { item: any, isChild?: boolean }) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = isActiveRoute(item.href);
    const isExpanded = isMenuExpanded(item.href);

    return (
      <li className={`${isChild ? 'ml-4' : ''}`}>
        <div className="flex items-center">
          {hasChildren ? (
            <button
              onClick={() => toggleMenu(item.href)}
              className={`flex items-center w-full px-4 py-3 text-left rounded-lg transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              {item.icon && (
                <MenuIcon_Component
                  icon={item.icon}
                  className={`mr-3 ${isActive ? 'text-white' : 'text-gray-500'}`}
                />
              )}
              <span className="flex-1 font-medium">{item.title}</span>
              {hasChildren && (
                <MenuIcon_Component
                  icon={isExpanded ? ChevronDown : ChevronRight}
                  className={`ml-2 ${isActive ? 'text-white' : 'text-gray-400'}`}
                />
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              className={`flex items-center w-full px-4 py-3 rounded-lg transition-all duration-200 ${
                isActive
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              {item.icon && (
                <MenuIcon_Component
                  icon={item.icon}
                  className={`mr-3 ${isActive ? 'text-white' : 'text-gray-500'}`}
                />
              )}
              <span className="font-medium">{item.title}</span>
            </Link>
          )}
        </div>

        {hasChildren && isExpanded && (
          <ul className="mt-2 space-y-1 ml-4">
            {item.children.map((child: any) => (
              <li key={child.href}>
                <Link
                  href={child.href}
                  className={`flex items-center px-4 py-2 text-sm rounded-lg transition-all duration-200 ${
                    pathname === child.href
                      ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-600'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <span className="w-2 h-2 bg-gray-300 rounded-full mr-3"></span>
                  {child.title}
                </Link>
              </li>
            ))}
          </ul>
        )}
      </li>
    );
  };

  return (
    <nav className="md:left-0 md:block md:fixed md:top-0 md:bottom-0 md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden flex flex-wrap items-center justify-between md:w-64 z-10 py-0 md:py-6 px-0 bg-white shadow-xl border-r border-gray-200">
      <div className="w-full">
        {/* Logo Section */}
        <div className="px-6 py-4 border-b border-gray-200">
          <DashboardLogo />
        </div>

        {/* Menu Section */}
        <div className="px-4 py-6">
          {isAdmin || adminMenu.length > 0 ? (
            <div className="space-y-2">
              {/* Admin/Manager Menu */}
              {adminMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {/* Separator and additional options */}
              <div className="pt-4 mt-6 border-t border-gray-200">
                <MenuItem
                  item={{
                    title: "Thông tin tài khoản",
                    href: "/dashboard/account",
                    icon: User
                  }}
                />
                <div className="mt-4">
                  <ButtonLogout />
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {/* General user menu */}
              {generalMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {hasPermission("user") && userMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              {hasPermission("manager") && ManagerMenu.map((item) => (
                <MenuItem key={item.href} item={item} />
              ))}

              <div className="pt-4 mt-6 border-t border-gray-200">
                <ButtonLogout />
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}
