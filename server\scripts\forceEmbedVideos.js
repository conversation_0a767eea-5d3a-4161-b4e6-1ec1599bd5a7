const mongoose = require('mongoose');
const Post = require('../api/models/post');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/blog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Force embed videos into content
const forceEmbedVideos = async () => {
  try {
    console.log('🔧 Force embedding videos into content...');
    
    const posts = await Post.find({
      video: { $exists: true, $ne: [] }
    });
    
    console.log(`Found ${posts.length} posts with videos`);
    let updatedCount = 0;
    
    for (const post of posts) {
      console.log(`\n📝 Processing post: ${post.title}`);
      console.log(`Post ID: ${post._id}`);
      console.log(`Current desc length: ${post.desc?.length || 0}`);
      console.log(`Video field:`, JSON.stringify(post.video));
      
      if (post.video && post.video.length > 0) {
        // Get video URL
        let videoUrl;
        if (typeof post.video === 'string') {
          videoUrl = post.video;
        } else if (Array.isArray(post.video)) {
          const firstVideo = post.video[0];
          if (typeof firstVideo === 'string') {
            videoUrl = firstVideo;
          } else if (typeof firstVideo === 'object' && firstVideo['0']) {
            videoUrl = firstVideo['0'];
          } else {
            videoUrl = Object.values(firstVideo)[0];
          }
        }
        
        console.log(`Video URL: ${videoUrl}`);
        
        if (videoUrl) {
          const currentDesc = post.desc || "";
          
          // Always force embed video (replace existing if any)
          const videoHtml = `<video controls preload="metadata" class="video-player prosemirror-video" style="width: 100%; max-width: 100%; height: auto;" src="${videoUrl}"></video>`;
          
          // Remove existing video tags first
          const cleanDesc = currentDesc.replace(/<video[^>]*>.*?<\/video>/gi, '').trim();
          
          // Add new video
          const updatedDesc = cleanDesc + "\n" + videoHtml;
          
          post.desc = updatedDesc;
          await post.save();
          
          console.log(`✅ Force embedded video for: ${post.title}`);
          console.log(`New desc length: ${updatedDesc.length}`);
          updatedCount++;
        }
      }
    }
    
    console.log(`\n✅ Force embedded videos in ${updatedCount} posts`);
  } catch (error) {
    console.error('❌ Error force embedding videos:', error);
  }
};

// Check current video content status
const checkVideoContent = async () => {
  try {
    console.log('📋 Checking video content status...');
    
    const posts = await Post.find({
      video: { $exists: true, $ne: [] }
    }).select('_id title video desc');
    
    console.log(`Found ${posts.length} posts with videos:`);
    
    posts.forEach((post, index) => {
      console.log(`\n${index + 1}. ${post.title}`);
      console.log(`   ID: ${post._id}`);
      console.log(`   Video count: ${post.video?.length || 0}`);
      console.log(`   Desc length: ${post.desc?.length || 0}`);
      console.log(`   Has video in desc: ${post.desc?.includes('<video') || false}`);
      
      if (post.desc?.includes('<video')) {
        const videoMatches = post.desc.match(/<video[^>]*>/g);
        console.log(`   Video tags found: ${videoMatches?.length || 0}`);
      }
    });
  } catch (error) {
    console.error('❌ Error checking video content:', error);
  }
};

// Main function
const main = async () => {
  console.log('🚀 Starting force video embed script...');
  
  await connectDB();
  
  // Check current status
  await checkVideoContent();
  
  console.log('\n' + '='.repeat(50));
  
  // Force embed videos
  await forceEmbedVideos();
  
  console.log('\n' + '='.repeat(50));
  
  // Check status after embedding
  console.log('\n📋 Status after force embedding:');
  await checkVideoContent();
  
  console.log('\n✅ Force video embed completed!');
  process.exit(0);
};

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { forceEmbedVideos, checkVideoContent };
