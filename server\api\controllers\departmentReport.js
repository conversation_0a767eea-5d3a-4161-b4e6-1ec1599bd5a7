const mongoose = require("mongoose");
const DepartmentReport = require("../models/departmentReport");
const Department = require("../models/department");
const User = require("../models/user");
const { ForbiddenError } = require("@casl/ability");
const defineAbilityFor = require("../permissions/abilities");

// Get all reports for a department
exports.getDepartmentReports = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { page = 1, perPage = 10, reportType, status, startDate, endDate } = req.query;
    const ability = defineAbilityFor(req.user);

    // Check if user can access department reports
    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canAccess = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId) ||
                     (req.user.department?.toString() === departmentId);

    if (!canAccess) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Build query options
    const options = {};
    if (reportType) options.reportType = reportType;
    if (status) options.status = status;
    if (startDate && endDate) {
      options.dateRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      };
    }

    const onTotal = DepartmentReport.countDocuments({ 
      department: departmentId,
      ...(reportType && { reportType }),
      ...(status && { status })
    });

    const onReports = DepartmentReport.findByDepartment(departmentId, options)
      .skip(perPage * page - perPage)
      .limit(perPage);

    const [total, reports] = await Promise.all([onTotal, onReports]);

    res.json({
      success: true,
      reports,
      total,
      page: parseInt(page),
      perPage: parseInt(perPage),
      totalPages: Math.ceil(total / perPage)
    });

  } catch (error) {
    console.error('Error getting department reports:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Get single report
exports.getReport = async (req, res) => {
  try {
    const { id } = req.params;

    const report = await DepartmentReport.findById(id)
      .populate('department', 'name code')
      .populate('generatedBy', 'username email')
      .populate('sharedWith.user', 'username email')
      .populate('comments.user', 'username email');

    if (!report) {
      return res.status(404).json({
        success: false,
        message: "Report not found"
      });
    }

    // Check access permission
    if (!report.canAccess(req.user)) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    res.json({
      success: true,
      report
    });

  } catch (error) {
    console.error('Error getting report:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Create new report
exports.createReport = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { 
      reportType, 
      title, 
      description, 
      startDate, 
      endDate,
      isPublic = false,
      tags = []
    } = req.body;

    // Validate required fields
    if (!reportType || !title || !startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: "Report type, title, start date, and end date are required"
      });
    }

    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canCreate = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId);

    if (!canCreate) {
      return res.status(403).json({
        success: false,
        message: "Only department admins can create reports"
      });
    }

    // Generate report data based on type
    const reportData = await generateReportData(departmentId, reportType, new Date(startDate), new Date(endDate));

    const report = new DepartmentReport({
      department: departmentId,
      reportType,
      title: title.trim(),
      description: description?.trim() || "",
      reportPeriod: {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      },
      data: reportData,
      generatedBy: req.user._id,
      status: "completed",
      isPublic,
      tags
    });

    await report.save();

    // Populate the saved report
    await report.populate([
      { path: 'department', select: 'name code' },
      { path: 'generatedBy', select: 'username email' }
    ]);

    res.status(201).json({
      success: true,
      report,
      message: "Report created successfully"
    });

  } catch (error) {
    console.error('Error creating report:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Update report
exports.updateReport = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, isPublic, tags } = req.body;

    const report = await DepartmentReport.findById(id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: "Report not found"
      });
    }

    // Permission check - only creator or department admin can update
    const canUpdate = req.user.systemRole === 'admin' ||
                     report.generatedBy.equals(req.user._id) ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === report.department.toString());

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Update fields
    if (title) report.title = title.trim();
    if (description !== undefined) report.description = description.trim();
    if (isPublic !== undefined) report.isPublic = isPublic;
    if (tags) report.tags = tags;

    await report.save();

    res.json({
      success: true,
      report,
      message: "Report updated successfully"
    });

  } catch (error) {
    console.error('Error updating report:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Delete report
exports.deleteReport = async (req, res) => {
  try {
    const { id } = req.params;

    const report = await DepartmentReport.findById(id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: "Report not found"
      });
    }

    // Permission check - only creator or department admin can delete
    const canDelete = req.user.systemRole === 'admin' ||
                     report.generatedBy.equals(req.user._id) ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === report.department.toString());

    if (!canDelete) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    await DepartmentReport.findByIdAndDelete(id);

    res.json({
      success: true,
      message: "Report deleted successfully"
    });

  } catch (error) {
    console.error('Error deleting report:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Add comment to report
exports.addComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { content } = req.body;

    if (!content || !content.trim()) {
      return res.status(400).json({
        success: false,
        message: "Comment content is required"
      });
    }

    const report = await DepartmentReport.findById(id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: "Report not found"
      });
    }

    // Check access permission
    if (!report.canAccess(req.user)) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    await report.addComment(req.user._id, content.trim());

    // Get updated report with populated comments
    const updatedReport = await DepartmentReport.findById(id)
      .populate('comments.user', 'username email');

    res.json({
      success: true,
      comments: updatedReport.comments,
      message: "Comment added successfully"
    });

  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Helper function to generate report data
async function generateReportData(departmentId, reportType, startDate, endDate) {
  const data = {
    memberStats: {},
    activityStats: {},
    performanceMetrics: {},
    fileStats: {},
    customData: {}
  };

  try {
    // Get member statistics
    const totalMembers = await User.countDocuments({ 
      department: departmentId, 
      private: false 
    });
    
    const activeMembers = await User.countDocuments({ 
      department: departmentId, 
      private: false,
      isActive: true
    });
    
    const newMembers = await User.countDocuments({ 
      department: departmentId, 
      private: false,
      createdAt: { $gte: startDate, $lte: endDate }
    });
    
    const departmentAdmins = await User.countDocuments({ 
      department: departmentId, 
      isDepartmentAdmin: true 
    });

    data.memberStats = {
      totalMembers,
      activeMembers,
      newMembers,
      departmentAdmins
    };

    // Add more statistics based on report type
    switch (reportType) {
      case 'monthly_summary':
        // Add comprehensive monthly data
        data.activityStats = {
          totalLogins: Math.floor(Math.random() * 1000), // Mock data
          averageSessionTime: Math.floor(Math.random() * 120),
          filesUploaded: Math.floor(Math.random() * 50),
          filesDownloaded: Math.floor(Math.random() * 200),
          announcementsCreated: Math.floor(Math.random() * 10),
          announcementsViewed: Math.floor(Math.random() * 500)
        };
        break;
        
      case 'member_activity':
        // Focus on member activity data
        data.activityStats = {
          totalLogins: Math.floor(Math.random() * 1000),
          averageSessionTime: Math.floor(Math.random() * 120)
        };
        break;
        
      case 'performance_metrics':
        // Focus on performance data
        data.performanceMetrics = {
          taskCompletionRate: Math.floor(Math.random() * 100),
          averageResponseTime: Math.floor(Math.random() * 60),
          userSatisfactionScore: Math.floor(Math.random() * 10),
          productivityIndex: Math.floor(Math.random() * 100)
        };
        break;
        
      case 'file_usage':
        // Focus on file statistics
        data.fileStats = {
          totalFiles: Math.floor(Math.random() * 100),
          totalFileSize: Math.floor(Math.random() * 1000000),
          mostAccessedFiles: [],
          fileTypeDistribution: [
            { fileType: 'PDF', count: Math.floor(Math.random() * 50), totalSize: Math.floor(Math.random() * 500000) },
            { fileType: 'DOC', count: Math.floor(Math.random() * 30), totalSize: Math.floor(Math.random() * 300000) },
            { fileType: 'XLS', count: Math.floor(Math.random() * 20), totalSize: Math.floor(Math.random() * 200000) }
          ]
        };
        break;
    }

  } catch (error) {
    console.error('Error generating report data:', error);
  }

  return data;
}

module.exports = {
  getDepartmentReports: exports.getDepartmentReports,
  getReport: exports.getReport,
  createReport: exports.createReport,
  updateReport: exports.updateReport,
  deleteReport: exports.deleteReport,
  addComment: exports.addComment
};
