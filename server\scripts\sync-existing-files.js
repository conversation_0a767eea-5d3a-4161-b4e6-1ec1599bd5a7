const mongoose = require('mongoose');
require('dotenv').config();

// Import models
require('../api/models/user');
require('../api/models/file');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Import sync function
const { syncExistingFiles } = require('../sync-existing-files');

// Run sync
async function runSync() {
  try {
    console.log('🚀 Starting file synchronization script...');
    await syncExistingFiles();
    console.log('✅ Synchronization completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Synchronization failed:', error);
    process.exit(1);
  }
}

// Handle MongoDB connection
mongoose.connection.on('connected', () => {
  console.log('📊 Connected to MongoDB');
  runSync();
});

mongoose.connection.on('error', (err) => {
  console.error('❌ MongoDB connection error:', err);
  process.exit(1);
});

mongoose.connection.on('disconnected', () => {
  console.log('📊 Disconnected from MongoDB');
});
