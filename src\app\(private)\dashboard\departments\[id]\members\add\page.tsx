"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { toast } from "react-toastify";
import Link from "next/link";
import { ArrowLeft, UserPlus, Search, Check } from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";
import PermissionGuard from "@/components/PermissionGuard";

interface User {
  _id: string;
  username: string;
  email: string;
  systemRole: string;
  department?: {
    _id: string;
    name: string;
  };
  isDepartmentAdmin: boolean;
}

export default function AddDepartmentMemberPage() {
  const params = useParams();
  const router = useRouter();
  const [department, setDepartment] = useState<Department | null>(null);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [adding, setAdding] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const departmentId = params.id as string;

  const fetchDepartment = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.getDepartment(departmentId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        setDepartment(response.payload.department);
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error: any) {
      console.error("Error fetching department:", error);
      toast.error("Có lỗi xảy ra khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    }
  };

  const searchUsers = async () => {
    if (!searchTerm.trim()) {
      setAvailableUsers([]);
      return;
    }

    try {
      setSearching(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.searchAvailableUsers(
        departmentId,
        searchTerm,
        sessionToken
      );
      
      if (response && response.payload && response.payload.success) {
        setAvailableUsers(response.payload.users);
      } else {
        toast.error("Không thể tìm kiếm người dùng");
      }
    } catch (error: any) {
      console.error("Error searching users:", error);
      toast.error("Có lỗi xảy ra khi tìm kiếm người dùng");
    } finally {
      setSearching(false);
    }
  };

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
    }
  }, [departmentId]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchUsers();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const handleUserSelect = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleAddMembers = async () => {
    if (selectedUsers.length === 0) {
      toast.warning("Vui lòng chọn ít nhất một người dùng");
      return;
    }

    try {
      setAdding(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.addMembers(
        departmentId,
        { userIds: selectedUsers },
        sessionToken
      );
      
      if (response && response.payload && response.payload.success) {
        toast.success(`Đã thêm ${selectedUsers.length} thành viên vào phòng ban`);
        router.push(`/dashboard/departments/${departmentId}/members`);
      } else {
        toast.error("Không thể thêm thành viên. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error adding members:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi thêm thành viên";
      toast.error(errorMessage);
    } finally {
      setAdding(false);
    }
  };

  useEffect(() => {
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-gray-600">Đang tải...</div>
        </div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy phòng ban</h2>
            <p className="text-gray-600 mb-4">Phòng ban bạn đang tìm kiếm không tồn tại.</p>
            <Link
              href="/dashboard/departments"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft size={16} className="mr-2" />
              Quay lại danh sách
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermissions={["system_departments_manage", "dept_members_manage"]} fallbackPath="/dashboard">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/departments/${departmentId}/members`}
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} className="mr-2" />
              Quay lại
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Thêm thành viên</h1>
              <p className="text-gray-600">{department.name} - {department.code}</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Search Users */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tìm kiếm người dùng</h3>
                
                {/* Search Input */}
                <div className="relative mb-6">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Nhập tên hoặc email để tìm kiếm..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                {/* Search Results */}
                {searching ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-gray-600">Đang tìm kiếm...</div>
                  </div>
                ) : searchTerm && availableUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <Search size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy người dùng</h3>
                    <p className="text-gray-600">Thử tìm kiếm với từ khóa khác.</p>
                  </div>
                ) : availableUsers.length > 0 ? (
                  <div className="space-y-3">
                    {availableUsers.map((user) => (
                      <div
                        key={user._id}
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedUsers.includes(user._id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handleUserSelect(user._id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <span className="text-sm font-medium text-gray-700">
                                  {user.username.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">{user.username}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                              {user.department && (
                                <div className="text-xs text-orange-600">
                                  Đã thuộc phòng ban: {user.department.name}
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.systemRole === 'admin'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {user.systemRole === 'admin' ? 'System Admin' : 'User'}
                            </span>
                            
                            {selectedUsers.includes(user._id) && (
                              <Check size={20} className="text-blue-600" />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Search size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Tìm kiếm người dùng</h3>
                    <p className="text-gray-600">Nhập tên hoặc email để tìm kiếm người dùng có thể thêm vào phòng ban.</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Selected Users & Actions */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 sticky top-6">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Đã chọn ({selectedUsers.length})
                </h3>
                
                {selectedUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <UserPlus size={48} className="mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-600">Chưa chọn người dùng nào</p>
                  </div>
                ) : (
                  <>
                    <div className="space-y-2 mb-6 max-h-64 overflow-y-auto">
                      {selectedUsers.map((userId) => {
                        const user = availableUsers.find(u => u._id === userId);
                        if (!user) return null;
                        
                        return (
                          <div key={userId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{user.username}</div>
                              <div className="text-xs text-gray-500">{user.email}</div>
                            </div>
                            <button
                              onClick={() => handleUserSelect(userId)}
                              className="text-red-600 hover:text-red-800"
                            >
                              ×
                            </button>
                          </div>
                        );
                      })}
                    </div>
                    
                    <button
                      onClick={handleAddMembers}
                      disabled={adding}
                      className="w-full inline-flex items-center justify-center px-4 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {adding ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Đang thêm...
                        </>
                      ) : (
                        <>
                          <UserPlus size={16} className="mr-2" />
                          Thêm {selectedUsers.length} thành viên
                        </>
                      )}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
}
