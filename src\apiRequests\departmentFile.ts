import http from "@/lib/http";

export interface DepartmentFile {
  _id: string;
  department: {
    _id: string;
    name: string;
    code: string;
  };
  fileName: string;
  originalName: string;
  description?: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  fileExtension: string;
  category: "document" | "image" | "video" | "audio" | "archive" | "spreadsheet" | "presentation" | "other";
  tags: string[];
  uploadedBy: {
    _id: string;
    username: string;
    email: string;
  };
  uploadedAt: string;
  version: string;
  parentFile?: string;
  isLatestVersion: boolean;
  versionHistory: Array<{
    version: string;
    uploadedBy: {
      _id: string;
      username: string;
      email: string;
    };
    uploadedAt: string;
    changes?: string;
    filePath: string;
    fileSize: number;
  }>;
  visibility: "public" | "department" | "restricted" | "private";
  accessPermissions: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    permissions: Array<"view" | "download" | "edit" | "delete" | "share">;
    grantedBy: {
      _id: string;
      username: string;
      email: string;
    };
    grantedAt: string;
  }>;
  status: "active" | "archived" | "deleted" | "quarantined";
  isScanned: boolean;
  scanResult: "clean" | "infected" | "suspicious" | "pending";
  scanDate?: string;
  metadata: {
    dimensions?: {
      width: number;
      height: number;
    };
    duration?: number;
    bitrate?: number;
    pageCount?: number;
    wordCount?: number;
    encoding?: string;
    compression?: string;
    checksum?: string;
  };
  downloads: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    downloadedAt: string;
    ipAddress?: string;
    userAgent?: string;
  }>;
  views: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    viewedAt: string;
    ipAddress?: string;
    userAgent?: string;
  }>;
  shares: Array<{
    sharedWith: {
      _id: string;
      username: string;
      email: string;
    };
    sharedBy: {
      _id: string;
      username: string;
      email: string;
    };
    sharedAt: string;
    permissions: Array<"view" | "download" | "edit">;
    expiresAt?: string;
    accessCount: number;
  }>;
  comments: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    content: string;
    createdAt: string;
    editedAt?: string;
    isEdited: boolean;
  }>;
  folder: string;
  isFavorite: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    addedAt: string;
  }>;
  analytics: {
    totalDownloads: number;
    totalViews: number;
    uniqueDownloads: number;
    uniqueViews: number;
    lastAccessed?: string;
  };
  expiresAt?: string;
  autoDelete: boolean;
  isBackedUp: boolean;
  backupPath?: string;
  lastBackup?: string;
  createdAt: string;
  updatedAt: string;
  fileSizeFormatted?: string;
  isExpired?: boolean;
  fileUrl?: string;
}

export interface UploadFileRequest {
  file: File;
  description?: string;
  category?: string;
  tags?: string;
  folder?: string;
  visibility?: string;
}

export interface UpdateFileRequest {
  description?: string;
  category?: string;
  tags?: string;
  folder?: string;
  visibility?: string;
}

export interface ShareFileRequest {
  userId: string;
  permissions?: Array<"view" | "download" | "edit">;
  expiresAt?: string;
}

export interface FileListResponse {
  success: boolean;
  files: DepartmentFile[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export interface FileResponse {
  success: boolean;
  file: DepartmentFile;
  message?: string;
}

const departmentFileApiRequest = {
  // Get all files for a department
  getDepartmentFiles: (
    departmentId: string,
    params: {
      page?: number;
      perPage?: number;
      category?: string;
      folder?: string;
      tags?: string;
      search?: string;
      uploadedBy?: string;
      sort?: string;
    },
    sessionToken: string
  ) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    
    return http.get<FileListResponse>(
      `/api/administrator/departments/${departmentId}/files?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    );
  },

  // Upload file
  uploadFile: (departmentId: string, data: UploadFileRequest, sessionToken: string) => {
    const formData = new FormData();
    formData.append('file', data.file);
    if (data.description) formData.append('description', data.description);
    if (data.category) formData.append('category', data.category);
    if (data.tags) formData.append('tags', data.tags);
    if (data.folder) formData.append('folder', data.folder);
    if (data.visibility) formData.append('visibility', data.visibility);

    return http.post<FileResponse>(`/api/administrator/departments/${departmentId}/files/upload`, formData, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Get single file
  getFile: (id: string, sessionToken: string) =>
    http.get<FileResponse>(`/api/administrator/departments/files/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Download file
  downloadFile: (id: string, sessionToken: string) =>
    http.get<Blob>(`/api/administrator/departments/files/${id}/download`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
      responseType: 'blob' as any,
    }),

  // Update file metadata
  updateFile: (id: string, data: UpdateFileRequest, sessionToken: string) =>
    http.put<FileResponse>(`/api/administrator/departments/files/${id}`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete file
  deleteFile: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(`/api/administrator/departments/files/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Share file
  shareFile: (id: string, data: ShareFileRequest, sessionToken: string) =>
    http.post<{ success: boolean; message: string }>(`/api/administrator/departments/files/${id}/share`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Add to favorites
  addToFavorites: (id: string, sessionToken: string) =>
    http.post<{ success: boolean; message: string }>(`/api/administrator/departments/files/${id}/favorite`, {}, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Remove from favorites
  removeFromFavorites: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(`/api/administrator/departments/files/${id}/favorite`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Add comment
  addComment: (id: string, content: string, sessionToken: string) =>
    http.post<{ success: boolean; message: string; comments: DepartmentFile['comments'] }>(`/api/administrator/departments/files/${id}/comments`, { content }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get file analytics
  getAnalytics: (id: string, sessionToken: string) =>
    http.get<{ success: boolean; analytics: DepartmentFile['analytics'] }>(`/api/administrator/departments/files/${id}/analytics`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default departmentFileApiRequest;
