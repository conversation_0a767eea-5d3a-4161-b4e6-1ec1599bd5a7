"use client";

import { useState, useEffect } from "react";
import { useReactTable, getCoreRowModel, flexRender } from "@tanstack/react-table";
import { toast } from "react-toastify";
import Link from "next/link";
import { Edit, Users, Settings, Plus, Trash2, RotateCcw } from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";

export default function DepartmentTable() {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showDeleted, setShowDeleted] = useState(false);
  const perPage = 10;

  const fetchDepartments = async () => {
    const data = { page, perPage, includeDeleted: showDeleted };
    setLoading(true);
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.getAllDepartments(data, sessionToken);
      
      if (response && response.payload) {
        const { total, departments: fetchedDepartments } = response.payload;
        setDepartments(fetchedDepartments || []);
        setTotalPages(Math.ceil(total / perPage));
      } else {
        console.warn("No payload in departments response");
        setDepartments([]);
      }
    } catch (error: any) {
      console.error("Error fetching departments:", error);
      setDepartments([]);
      toast.error("Có lỗi xảy ra khi tải danh sách phòng ban. Vui lòng thử lại.");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDepartment = async (departmentId: string, departmentName: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa phòng ban "${departmentName}"? Hành động này có thể hoàn tác.`)) {
      return;
    }

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.deleteDepartment(departmentId, sessionToken);

      if (response && response.payload && response.payload.success) {
        toast.success("Xóa phòng ban thành công");
        fetchDepartments(); // Refresh the list
      } else {
        toast.error("Không thể xóa phòng ban");
      }
    } catch (error: any) {
      console.error("Error deleting department:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi xóa phòng ban";
      toast.error(errorMessage);
    }
  };

  const handleRestoreDepartment = async (departmentId: string, departmentName: string) => {
    if (!confirm(`Bạn có chắc chắn muốn khôi phục phòng ban "${departmentName}"?`)) {
      return;
    }

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.restoreDepartment(departmentId, sessionToken);

      if (response && response.payload && response.payload.success) {
        toast.success("Khôi phục phòng ban thành công");
        fetchDepartments(); // Refresh the list
      } else {
        toast.error("Không thể khôi phục phòng ban");
      }
    } catch (error: any) {
      console.error("Error restoring department:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi khôi phục phòng ban";
      toast.error(errorMessage);
    }
  };

  useEffect(() => {
    fetchDepartments();
  }, [page, showDeleted]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  const columns = [
    { 
      accessorKey: "name", 
      header: "Tên Phòng Ban",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <span className="font-medium">{row.original.name}</span>
          <span className="text-xs bg-gray-100 px-2 py-1 rounded">
            {row.original.code}
          </span>
        </div>
      )
    },
    { 
      accessorKey: "description", 
      header: "Mô Tả",
      cell: ({ row }: any) => (
        <span className="text-gray-600 text-sm">
          {row.original.description || "Không có mô tả"}
        </span>
      )
    },
    {
      accessorKey: "departmentAdmin",
      header: "Quản Lý",
      cell: ({ row }: any) => (
        <div className="text-sm">
          {row.original.departmentAdmin ? (
            <div>
              <div className="font-medium">{row.original.departmentAdmin.username}</div>
              <div className="text-gray-500">{row.original.departmentAdmin.email}</div>
            </div>
          ) : (
            <span className="text-gray-400 italic">Chưa có quản lý</span>
          )}
        </div>
      )
    },
    {
      accessorKey: "memberCount",
      header: "Thành Viên",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-1">
          <Users size={16} className="text-gray-500" />
          <span>{row.original.memberCount || 0}</span>
        </div>
      )
    },
    {
      accessorKey: "isActive",
      header: "Trạng Thái",
      cell: ({ row }: any) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          row.original.isActive
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {row.original.isActive ? 'Hoạt động' : 'Tạm dừng'}
        </span>
      )
    },
    {
      accessorKey: "createdAt",
      header: "Ngày Tạo",
      cell: ({ row }: any) =>
        new Date(row.original.createdAt).toLocaleDateString("vi-VN"),
    },
    {
      accessorKey: "actions",
      header: "Thao Tác",
      cell: ({ row }: any) => (
        <div className="flex items-center space-x-2">
          <Link
            href={`/dashboard/departments/${row.original._id}`}
            className="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-50"
            title="Chỉnh sửa"
          >
            <Edit size={16} />
          </Link>
          <Link
            href={`/dashboard/departments/${row.original._id}/members`}
            className="text-green-600 hover:text-green-800 p-1 rounded hover:bg-green-50"
            title="Quản lý thành viên"
          >
            <Users size={16} />
          </Link>
          <Link
            href={`/dashboard/departments/${row.original._id}/settings`}
            className="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-50"
            title="Cài đặt"
          >
            <Settings size={16} />
          </Link>
          {row.original.isActive ? (
            <button
              onClick={() => handleDeleteDepartment(row.original._id, row.original.name)}
              className="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50"
              title="Xóa phòng ban"
            >
              <Trash2 size={16} />
            </button>
          ) : (
            <button
              onClick={() => handleRestoreDepartment(row.original._id, row.original.name)}
              className="text-green-600 hover:text-green-800 p-1 rounded hover:bg-green-50"
              title="Khôi phục phòng ban"
            >
              <RotateCcw size={16} />
            </button>
          )}
        </div>
      ),
    },
  ];

  const table = useReactTable({
    data: departments || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (loading) {
    return (
      <div className="w-full p-4 flex items-center justify-center">
        <div className="text-gray-600">Đang tải dữ liệu...</div>
      </div>
    );
  }

  return (
    <div className="w-full p-4">
      {/* Header with Add Button */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-gray-900">Quản Lý Phòng Ban</h2>
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={showDeleted}
              onChange={(e) => setShowDeleted(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-gray-600">Hiển thị phòng ban đã xóa</span>
          </label>
        </div>
        <Link
          href="/dashboard/departments/add"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={16} className="mr-2" />
          Thêm Phòng Ban
        </Link>
      </div>

      {departments.length === 0 ? (
        <div className="text-center text-gray-600 py-8">
          <div className="mb-4">
            <Users size={48} className="mx-auto text-gray-300" />
          </div>
          <p className="text-lg mb-2">Chưa có phòng ban nào</p>
          <p className="text-sm text-gray-500 mb-4">
            Tạo phòng ban đầu tiên để bắt đầu quản lý tổ chức
          </p>
          <Link
            href="/dashboard/departments/add"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Tạo Phòng Ban
          </Link>
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="table-auto w-full border-collapse">
              <thead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <tr key={headerGroup.id} className="bg-gray-50 border-b">
                    {headerGroup.headers.map((header) => (
                      <th
                        key={header.id}
                        className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </th>
                    ))}
                  </tr>
                ))}
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="hover:bg-gray-50">
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="px-4 py-4 whitespace-nowrap text-sm">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-700">
                Trang {page} / {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(page - 1)}
                  disabled={page === 1}
                  className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300"
                >
                  Trước
                </button>
                <button
                  onClick={() => handlePageChange(page + 1)}
                  disabled={page === totalPages}
                  className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300"
                >
                  Sau
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
