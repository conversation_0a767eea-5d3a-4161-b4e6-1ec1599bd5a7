const mongoose = require("mongoose");

const { Schema } = mongoose;
const bcrypt = require("bcryptjs");
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const userSchema = new Schema(
  {
    username: { type: String, required: false },
    phonenumber: { type: String, unique: true, sparse: true, required: false  },
    email: {
      type: String,
      required: true,
      unique: true,
      match:
        // eslint-disable-next-line no-useless-escape
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    },
    password: {
      type: String,
      required: true,
    },
    resetlink: {
      type: String,
      default: "",
    },
    private: {
      type: Boolean,
      default: false,
    },
    isMail: {
      type: Boolean,
      default: false,
    },
    isAuthApp: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: String,
    // System role - only 'admin' for system administrators, null for department users
    systemRole: {
      type: String,
      enum: ["admin", null],
      default: null
    },

    // Department assignment
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      default: null
    },

    // Department admin flag
    isDepartmentAdmin: {
      type: Boolean,
      default: false
    },

    permissions: [
      {
        type: String,
        enum: [
          // System-wide permissions (only for system admins)
          "system_admin_full_access",
          "system_departments_manage",
          "system_users_manage",
          "system_settings_manage",

          // Department File Management
          "dept_file_view",
          "dept_file_upload",
          "dept_file_delete",

          // Department Settings
          "dept_settings_view",
          "dept_settings_edit",

          // Department Reports
          "dept_reports_view",
          "dept_reports_create",
          "dept_reports_export",

          // Department Communication
          "dept_announcements_view",
          "dept_announcements_create",
          "dept_announcements_edit",
          "dept_announcements_delete",

          // Department Posts
          "dept_posts_view",
          "dept_posts_create",
          "dept_posts_edit",
          "dept_posts_delete",

          // Court Case Management
          "court_case_view",
          "court_case_edit",
          "court_case_export",
          "court_case_detailed_stats_view",
          "court_case_import",
          "court_case_delete",
          "court_case_create",
          "court_case_stats_view",

          // News Management
          "news_view",
          "news_edit",
          "news_publish",
          "news_featured",
          "news_delete",
          "news_create",

          // General permissions
          "file_view",
          "file_upload",
          "file_delete",
          "system_settings_view",
          "system_settings_edit",
          "permissions_manage",

          // Additional common permissions
          "posts_view",
          "posts_create", 
          "posts_edit",
          "posts_delete",
          "posts_publish",
          "categories_view",
          "categories_manage",
          "users_view",
          "users_manage"
        ]
      },
    ],
    point: { type: Number, default: 0 },
    code: { type: String, index: true, unique: true },
    Passcode: { type: Schema.Types.ObjectId, ref: "Passcode" },
    avatar: { type: String },
    address: { type: Schema.Types.ObjectId, ref: "Address" },
    rank: { type: String, enum: ["1", "2", "3", "4", "5"], default: "1" },
    bio: { type: String },
    gender: { type: String, enum: ["Male", "Female", "Not"], default: "Not" },
  },
  { timestamps: true }
);

userSchema.plugin(accessibleRecordsPlugin);

userSchema.methods.comparePassword = function (password) {
  const user = this;
  return bcrypt.compareSync(password, user.password);
};

// Check if user is system admin
userSchema.methods.isSystemAdmin = function() {
  return this.systemRole === 'admin';
};

// Check if user is department admin
userSchema.methods.isDeptAdmin = function() {
  return this.isDepartmentAdmin && this.department;
};

// Check if user can manage another user
userSchema.methods.canManageUser = function(targetUser) {
  // System admin can manage anyone
  if (this.isSystemAdmin()) return true;

  // Department admin can only manage users in same department
  if (this.isDeptAdmin() && targetUser.department) {
    return this.department.equals(targetUser.department);
  }

  return false;
};

// Get effective permissions (including department defaults)
userSchema.methods.getEffectivePermissions = function() {
  let effectivePermissions = [...this.permissions];

  // System admin gets all system permissions
  if (this.isSystemAdmin()) {
    effectivePermissions.push('system_admin_full_access');
  }

  return [...new Set(effectivePermissions)]; // Remove duplicates
};

// Check if user has specific permission
userSchema.methods.hasPermission = function(permission) {
  // System admin has all permissions
  if (this.isSystemAdmin()) return true;

  const effectivePermissions = this.getEffectivePermissions();
  return effectivePermissions.includes(permission);
};

// Static methods
userSchema.statics.findByDepartment = function(departmentId) {
  return this.find({
    department: departmentId,
    private: false
  }).populate('department', 'name code');
};

userSchema.statics.findDepartmentAdmins = function() {
  return this.find({
    isDepartmentAdmin: true,
    private: false
  }).populate('department', 'name code');
};

userSchema.statics.findSystemAdmins = function() {
  return this.find({
    systemRole: 'admin',
    private: false
  });
};

// Indexes for better performance
userSchema.index({ department: 1 });
userSchema.index({ isDepartmentAdmin: 1 });
userSchema.index({ systemRole: 1 });
userSchema.index({ department: 1, isDepartmentAdmin: 1 });

// Validation
userSchema.pre('save', function(next) {
  // If user is system admin, they cannot be in a department
  if (this.systemRole === 'admin') {
    this.department = null;
    this.isDepartmentAdmin = false;
  }

  // If user is department admin, they must be in a department
  if (this.isDepartmentAdmin && !this.department) {
    return next(new Error('Department admin must be assigned to a department'));
  }

  next();
});

userSchema.index({ phonenumber: 1 }, { unique: true, partialFilterExpression: { phonenumber: { $exists: true } } });

module.exports = mongoose.model("User", userSchema);
