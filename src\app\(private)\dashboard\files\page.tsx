"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { 
  Upload, 
  File, 
  Image, 
  Video, 
  Download, 
  Trash2, 
  Search,
  Filter,
  Grid,
  List,
  Plus
} from "react-feather";
import fileApiRequest from "@/apiRequests/file";
import PermissionGuard from "@/components/PermissionGuard";

interface FileItem {
  _id: string;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  uploadedBy: {
    _id: string;
    username: string;
  };
  uploadedAt: string;
  url: string;
}

export default function FilesPage() {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalFiles, setTotalFiles] = useState(0);
  const perPage = 20;

  const fetchFiles = async () => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await fileApiRequest.getFiles({
        page: currentPage,
        perPage,
        search: searchTerm,
        type: filterType !== "all" ? filterType : undefined
      }, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        setFiles(response.payload.files);
        setTotalFiles(response.payload.total);
      } else {
        toast.error("Không thể tải danh sách file");
      }
    } catch (error: any) {
      console.error("Error fetching files:", error);
      toast.error("Có lỗi xảy ra khi tải danh sách file");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, [currentPage, searchTerm, filterType]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const formData = new FormData();
      formData.append("file", file);

      const response = await fileApiRequest.uploadFile(formData, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        toast.success("Upload file thành công!");
        fetchFiles(); // Refresh the list
      } else {
        toast.error("Không thể upload file. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error uploading file:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi upload file";
      toast.error(errorMessage);
    } finally {
      setUploading(false);
      // Reset input
      event.target.value = "";
    }
  };

  const handleDeleteFile = async (fileId: string, filename: string) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa file "${filename}"?`)) {
      return;
    }

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await fileApiRequest.deleteFile(fileId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        toast.success("Xóa file thành công!");
        fetchFiles(); // Refresh the list
      } else {
        toast.error("Không thể xóa file. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error deleting file:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi xóa file";
      toast.error(errorMessage);
    }
  };

  const getFileIcon = (mimetype: string) => {
    if (mimetype.startsWith("image/")) return <Image size={20} className="text-green-600" />;
    if (mimetype.startsWith("video/")) return <Video size={20} className="text-purple-600" />;
    return <File size={20} className="text-blue-600" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.originalName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || 
                       (filterType === "image" && file.mimetype.startsWith("image/")) ||
                       (filterType === "video" && file.mimetype.startsWith("video/")) ||
                       (filterType === "document" && !file.mimetype.startsWith("image/") && !file.mimetype.startsWith("video/"));
    return matchesSearch && matchesType;
  });

  const totalPages = Math.ceil(totalFiles / perPage);

  return (
    <PermissionGuard requiredPermissions={["dept_files_view", "system_files_manage"]} fallbackPath="/dashboard">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Quản lý File</h1>
            <p className="text-gray-600">Quản lý và chia sẻ file trong hệ thống.</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <label className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors cursor-pointer">
              <Upload size={16} className="mr-2" />
              {uploading ? "Đang upload..." : "Upload File"}
              <input
                type="file"
                onChange={handleFileUpload}
                disabled={uploading}
                className="hidden"
                accept="*/*"
              />
            </label>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-4">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm file..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="flex items-center space-x-4">
                {/* Filter */}
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">Tất cả file</option>
                  <option value="image">Hình ảnh</option>
                  <option value="video">Video</option>
                  <option value="document">Tài liệu</option>
                </select>

                {/* View Mode */}
                <div className="flex border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 ${viewMode === "grid" ? "bg-blue-600 text-white" : "text-gray-600 hover:bg-gray-50"}`}
                  >
                    <Grid size={16} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 ${viewMode === "list" ? "bg-blue-600 text-white" : "text-gray-600 hover:bg-gray-50"}`}
                  >
                    <List size={16} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Files */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-gray-600">Đang tải danh sách file...</div>
              </div>
            ) : filteredFiles.length === 0 ? (
              <div className="text-center py-12">
                <File size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có file nào</h3>
                <p className="text-gray-600">Upload file đầu tiên để bắt đầu.</p>
              </div>
            ) : viewMode === "grid" ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {filteredFiles.map((file) => (
                  <div key={file._id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex flex-col items-center text-center">
                      <div className="mb-3">
                        {getFileIcon(file.mimetype)}
                      </div>
                      <h4 className="text-sm font-medium text-gray-900 mb-1 truncate w-full" title={file.originalName}>
                        {file.originalName}
                      </h4>
                      <p className="text-xs text-gray-500 mb-2">{formatFileSize(file.size)}</p>
                      <div className="flex space-x-2">
                        <a
                          href={file.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Download size={14} />
                        </a>
                        <button
                          onClick={() => handleDeleteFile(file._id, file.originalName)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        File
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Kích thước
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Người upload
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ngày upload
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thao tác
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredFiles.map((file) => (
                      <tr key={file._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="mr-3">
                              {getFileIcon(file.mimetype)}
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">{file.originalName}</div>
                              <div className="text-sm text-gray-500">{file.mimetype}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatFileSize(file.size)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {file.uploadedBy.username}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(file.uploadedAt).toLocaleDateString("vi-VN")}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <a
                            href={file.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Tải xuống
                          </a>
                          <button
                            onClick={() => handleDeleteFile(file._id, file.originalName)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Xóa
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700">
                  Hiển thị {(currentPage - 1) * perPage + 1} đến {Math.min(currentPage * perPage, totalFiles)} trong tổng số {totalFiles} file
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Trước
                  </button>
                  <span className="px-3 py-1 text-sm font-medium text-gray-700">
                    Trang {currentPage} / {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Sau
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </PermissionGuard>
  );
}
