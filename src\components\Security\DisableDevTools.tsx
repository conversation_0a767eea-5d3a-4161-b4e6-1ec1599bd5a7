"use client";

import { useEffect } from 'react';

const DisableDevTools = () => {
  useEffect(() => {
    // SECURITY DISABLED - Developer tools are now allowed everywhere
    console.log('🔓 Developer Tools Security: DISABLED');
    return; // Exit early, don't apply any security measures

    // Note: Right-click is now allowed for better user experience

    // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
    const disableKeyboardShortcuts = (e: KeyboardEvent) => {
      // F12
      if (e.keyCode === 123) {
        e.preventDefault();
        return false;
      }

      // Ctrl+Shift+I (Developer Tools)
      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
        e.preventDefault();
        return false;
      }

      // Ctrl+Shift+J (Console)
      if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
        e.preventDefault();
        return false;
      }

      // Ctrl+U (View Source)
      if (e.ctrl<PERSON><PERSON> && e.keyCode === 85) {
        e.preventDefault();
        return false;
      }

      // Ctrl+Shift+C (Inspect Element)
      if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
        e.preventDefault();
        return false;
      }

      // Ctrl+S (Save Page)
      if (e.ctrlKey && e.keyCode === 83) {
        e.preventDefault();
        return false;
      }
    };

    // Detect DevTools opening
    const detectDevTools = () => {
      const threshold = 160;

      setInterval(() => {
        if (
          window.outerHeight - window.innerHeight > threshold ||
          window.outerWidth - window.innerWidth > threshold
        ) {
          // DevTools detected - show warning
          console.clear();
          document.body.innerHTML = `
            <div style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: #000;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              font-family: Arial, sans-serif;
              font-size: 24px;
              z-index: 999999;
            ">
              <div style="text-align: center;">
                <h1>⚠️ Cảnh báo bảo mật</h1>
                <p>Developer Tools không được phép sử dụng!</p>
                <p>Vui lòng đóng Developer Tools để tiếp tục.</p>
              </div>
            </div>
          `;
        }
      }, 500);
    };

    // Clear console periodically
    const clearConsole = () => {
      setInterval(() => {
        console.clear();
        console.log('%cStop!', 'color: red; font-size: 50px; font-weight: bold;');
        console.log('%cThis is a browser feature intended for developers.', 'color: red; font-size: 16px;');
      }, 1000);
    };

    // Apply security measures (right-click is now allowed)
    // Only apply in production AND when not on localhost
    const shouldApplySecurity = process.env.NODE_ENV === 'production' &&
                               !window.location.hostname.includes('localhost') &&
                               !window.location.hostname.includes('127.0.0.1');

    if (shouldApplySecurity) {
      document.addEventListener('keydown', disableKeyboardShortcuts);
      document.body.classList.add('security-disabled');
      detectDevTools();
      clearConsole();
    }

    // Cleanup
    return () => {
      if (shouldApplySecurity) {
        document.removeEventListener('keydown', disableKeyboardShortcuts);
        document.body.classList.remove('security-disabled');
      }
    };
  }, []);

  return null;
};

export default DisableDevTools;