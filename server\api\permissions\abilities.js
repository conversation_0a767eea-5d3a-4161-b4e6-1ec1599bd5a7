const { AbilityBuilder, Ability, defineAbility } = require("@casl/ability");

let ANONYMOUS_ABILITY;

const defineAbilityFor = (user) => {
  if (user) {
    return new Ability(defineRulesFor(user));
  }
  ANONYMOUS_ABILITY = ANONYMOUS_ABILITY || new Ability(defineRulesFor({}));
  return ANONYMOUS_ABILITY;
};

function defineRulesFor(user) {
  const builder = new AbilityBuilder(Ability);

  if (!user || !user._id) {
    defineAnonymousRules(builder, user);
    return builder.rules;
  }

  // New department-based permission system
  if (user.systemRole === "admin") {
    defineSystemAdminRules(builder, user);
  } else if (user.isDepartmentAdmin && user.department) {
    defineDepartmentAdminRules(builder, user);
  } else if (user.department) {
    defineDepartmentUserRules(builder, user);
  } else {
    // Legacy support for users without departments
    defineLegacyRules(builder, user);
  }

  return builder.rules;
}

// System Administrator - full access to everything
function defineSystemAdminRules({ can }, user) {
  can("manage", "all"); // Full access to everything
}

// Department Administrator - can manage their department
function defineDepartmentAdminRules({ can, cannot }, user) {
  // Can manage their own profile
  can(["read", "update"], ["User"], { _id: user._id });

  // Can manage users in their department
  can(["read", "create", "update", "delete"], ["User"], { department: user.department });

  // Can manage department files
  can(["read", "create", "update", "delete"], ["File"], { department: user.department });

  // Can read their department info
  can(["read", "update"], ["Department"], { _id: user.department });

  // Can access department analytics
  can("read", "dashboard");

  // General permissions
  can(["read", "update", "modify"], ["Post", "Setting"]);

  // Check for specific department permissions
  if (user.permissions) {
    user.permissions.forEach(permission => {
      switch(permission) {
        case 'dept_user_view':
          can("read", "User", { department: user.department });
          break;
        case 'dept_user_add':
          can("create", "User", { department: user.department });
          break;
        case 'dept_user_edit':
          can("update", "User", { department: user.department });
          break;
        case 'dept_user_delete':
          can("delete", "User", { department: user.department });
          break;
        case 'dept_file_view':
          can("read", "File", { department: user.department });
          break;
        case 'dept_file_upload':
          can("create", "File");
          break;
        case 'dept_file_delete':
          can("delete", "File", { department: user.department });
          break;
        case 'dept_analytics_view':
          can("read", "dashboard");
          break;
      }
    });
  }
}

// Department User - limited access within department
function defineDepartmentUserRules({ can, cannot }, user) {
  // Can manage their own profile
  can(["read", "update"], ["User"], { _id: user._id });

  // Can view other users in same department (if has permission)
  if (user.permissions && user.permissions.includes('dept_user_view')) {
    can("read", "User", { department: user.department });
  }

  // Can view department files (if has permission)
  if (user.permissions && user.permissions.includes('dept_file_view')) {
    can("read", "File", { department: user.department });
  }

  // Can upload files (if has permission)
  if (user.permissions && user.permissions.includes('dept_file_upload')) {
    can("create", "File");
  }

  // Can view analytics (if has permission)
  if (user.permissions && user.permissions.includes('dept_analytics_view')) {
    can("read", "dashboard");
  }

  // General permissions
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  can("read", ["Setting"]);
}

// Legacy support for users without departments (during migration)
function defineLegacyRules({ can, cannot }, user) {
  // Basic permissions for legacy users
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  can("read", ["Setting"]);

  // Check for legacy permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }

    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Dashboard access
    if (user.permissions.includes('analytics_view')) {
      can("read", "dashboard");
    }
  }
}

function defineAnonymousRules({ can }) {
  can("read", ["Post", "Category", "Setting"]);
}

module.exports = defineAbilityFor;
