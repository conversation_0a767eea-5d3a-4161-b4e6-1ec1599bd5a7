const { AbilityBuilder, Ability, defineAbility } = require("@casl/ability");

let ANONYMOUS_ABILITY;

const defineAbilityFor = (user) => {
  if (user) {
    return new Ability(defineRulesFor(user));
  }
  ANONYMOUS_ABILITY = ANONYMOUS_ABILITY || new Ability(defineRulesFor({}));
  return ANONYMOUS_ABILITY;
};

function defineRulesFor(user) {
  const builder = new AbilityBuilder(Ability);

  if (!user || !user._id) {
    defineAnonymousRules(builder, user);
    return builder.rules;
  }

  // New department-based permission system
  if (user.systemRole === "admin") {
    defineSystemAdminRules(builder, user);
  } else if (user.isDepartmentAdmin && user.department) {
    defineDepartmentAdminRules(builder, user);
  } else if (user.department) {
    defineDepartmentUserRules(builder, user);
  } else {
    // Legacy support for users without departments
    defineLegacyRules(builder, user);
  }

  return builder.rules;
}

// System Administrator - full access to everything
function defineSystemAdminRules({ can }, user) {
  can("manage", "all"); // Full access to everything
}

// Department Administrator - can manage their department
function defineDepartmentAdminRules({ can, cannot }, user) {
  // Can manage their own profile
  can(["read", "update"], ["User"], { _id: user._id });

  // Can manage users in their department
  can(["read", "create", "update", "delete"], ["User"], { department: user.department });

  // Department management
  can(["read", "update"], ["Department"], { _id: user.department });

  // Department files management
  can(["read", "create", "update", "delete"], ["DepartmentFile"], { department: user.department });

  // Department announcements management
  can(["read", "create", "update", "delete"], ["DepartmentAnnouncement"], { department: user.department });

  // Department reports management
  can(["read", "create", "update", "delete"], ["DepartmentReport"], { department: user.department });

  // Advanced permissions for department admins
  can("manage", "dept_members");
  can("manage", "dept_settings");
  can("manage", "dept_workflow");
  can("manage", "dept_permissions");
  can("manage", "dept_files");
  can("manage", "dept_announcements");
  can("manage", "dept_reports");
  can("manage", "dept_dashboard");
  can("manage", "dept_analytics");
  can("manage", "dept_integrations");

  // Can read their department info
  can(["read", "update"], ["Department"], { _id: user.department });

  // Can access department analytics
  can("read", "dashboard");

  // General permissions
  can(["read", "update", "modify"], ["Post", "Setting"]);

  // Check for specific department permissions
  if (user.permissions) {
    user.permissions.forEach(permission => {
      switch(permission) {
        case 'dept_user_view':
          can("read", "User", { department: user.department });
          break;
        case 'dept_user_add':
          can("create", "User", { department: user.department });
          break;
        case 'dept_user_edit':
          can("update", "User", { department: user.department });
          break;
        case 'dept_user_delete':
          can("delete", "User", { department: user.department });
          break;
        case 'dept_file_view':
          can("read", "File", { department: user.department });
          break;
        case 'dept_file_upload':
          can("create", "File");
          break;
        case 'dept_file_delete':
          can("delete", "File", { department: user.department });
          break;
        case 'dept_analytics_view':
          can("read", "dashboard");
          break;
      }
    });
  }
}

// Department User - limited access within department
function defineDepartmentUserRules({ can, cannot }, user) {
  // Can manage their own profile
  can(["read", "update"], ["User"], { _id: user._id });

  // Basic department access
  can("read", ["Department"], { _id: user.department });

  // Check for specific department permissions
  if (user.permissions && user.permissions.length > 0) {
    user.permissions.forEach(permission => {
      switch(permission) {
        // Member permissions
        case 'dept_members_view':
          can("read", "User", { department: user.department });
          break;
        case 'dept_members_invite':
          can("create", "User", { department: user.department });
          break;
        case 'dept_members_edit':
          can("update", "User", { department: user.department });
          break;
        case 'dept_members_remove':
          can("delete", "User", { department: user.department });
          break;

        // File permissions
        case 'dept_files_view':
          can("read", "DepartmentFile", { department: user.department });
          break;
        case 'dept_files_upload':
          can("create", "DepartmentFile", { department: user.department });
          break;
        case 'dept_files_edit':
          can("update", "DepartmentFile", { department: user.department });
          break;
        case 'dept_files_delete':
          can("delete", "DepartmentFile", { department: user.department });
          break;
        case 'dept_files_share':
          can("share", "DepartmentFile", { department: user.department });
          break;

        // Announcement permissions
        case 'dept_announcements_view':
          can("read", "DepartmentAnnouncement", { department: user.department });
          break;
        case 'dept_announcements_create':
          can("create", "DepartmentAnnouncement", { department: user.department });
          break;
        case 'dept_announcements_edit':
          can("update", "DepartmentAnnouncement", { department: user.department });
          break;
        case 'dept_announcements_delete':
          can("delete", "DepartmentAnnouncement", { department: user.department });
          break;
        case 'dept_announcements_comment':
          can("comment", "DepartmentAnnouncement", { department: user.department });
          break;

        // Report permissions
        case 'dept_reports_view':
          can("read", "DepartmentReport", { department: user.department });
          break;
        case 'dept_reports_create':
          can("create", "DepartmentReport", { department: user.department });
          break;
        case 'dept_reports_edit':
          can("update", "DepartmentReport", { department: user.department });
          break;
        case 'dept_reports_delete':
          can("delete", "DepartmentReport", { department: user.department });
          break;
        case 'dept_reports_export':
          can("export", "DepartmentReport", { department: user.department });
          break;

        // Dashboard and analytics permissions
        case 'dept_dashboard_view':
          can("read", "dept_dashboard");
          break;
        case 'dept_analytics_view':
          can("read", "dept_analytics");
          break;
        case 'dept_analytics_export':
          can("export", "dept_analytics");
          break;

        // Settings permissions
        case 'dept_settings_view':
          can("read", "dept_settings");
          break;
        case 'dept_settings_edit':
          can("update", "dept_settings");
          break;
        case 'dept_workflow_manage':
          can("manage", "dept_workflow");
          break;
        case 'dept_permissions_manage':
          can("manage", "dept_permissions");
          break;

        // Integration permissions
        case 'dept_integrations_view':
          can("read", "dept_integrations");
          break;
        case 'dept_integrations_manage':
          can("manage", "dept_integrations");
          break;
      }
    });
  } else {
    // Default permissions for department users without specific permissions
    can("read", "DepartmentFile", { department: user.department, visibility: { $in: ["public", "department"] } });
    can("read", "DepartmentAnnouncement", { department: user.department, status: "published" });
    can("read", "DepartmentReport", { department: user.department, isPublic: true });
  }
  if (user.permissions && user.permissions.includes('dept_user_view')) {
    can("read", "User", { department: user.department });
  }

  // Can view department files (if has permission)
  if (user.permissions && user.permissions.includes('dept_file_view')) {
    can("read", "File", { department: user.department });
  }

  // Can upload files (if has permission)
  if (user.permissions && user.permissions.includes('dept_file_upload')) {
    can("create", "File");
  }

  // Can view analytics (if has permission)
  if (user.permissions && user.permissions.includes('dept_analytics_view')) {
    can("read", "dashboard");
  }

  // General permissions
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  can("read", ["Setting"]);
}

// Legacy support for users without departments (during migration)
function defineLegacyRules({ can, cannot }, user) {
  // Basic permissions for legacy users
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  can("read", ["Setting"]);

  // Check for legacy permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }

    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Dashboard access
    if (user.permissions.includes('analytics_view')) {
      can("read", "dashboard");
    }
  }
}

function defineAnonymousRules({ can }) {
  can("read", ["Post", "Category", "Setting"]);
}

module.exports = defineAbilityFor;
