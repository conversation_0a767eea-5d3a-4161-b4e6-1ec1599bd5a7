"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Save, X } from "react-feather";

const departmentPermissions = [
  { id: "dept_file_view", label: "Xem file phòng ban", category: "Quản lý file" },
  { id: "dept_file_upload", label: "Tải lên file", category: "Quản lý file" },
  { id: "dept_file_delete", label: "Xóa file", category: "Quản lý file" },

  { id: "dept_analytics_view", label: "Xem báo cáo phòng ban", category: "Báo cáo" },
  { id: "dept_reports_view", label: "Xem báo cáo", category: "Báo cáo" },
  { id: "dept_reports_create", label: "Tạo báo cáo", category: "Báo cáo" },
  { id: "dept_reports_export", label: "Xuất báo cáo", category: "Báo cáo" },

  { id: "dept_announcements_view", label: "Xem thông báo", category: "Thông báo" },
  { id: "dept_announcements_create", label: "Tạo thông báo", category: "Thông báo" },
  { id: "dept_announcements_edit", label: "Sửa thông báo", category: "Thông báo" },
  { id: "dept_announcements_delete", label: "Xóa thông báo", category: "Thông báo" },

  { id: "dept_settings_view", label: "Xem cài đặt phòng ban", category: "Cài đặt" },
  { id: "dept_settings_edit", label: "Sửa cài đặt phòng ban", category: "Cài đặt" },
];

const departmentSchema = z.object({
  name: z.string().min(2, "Tên phòng ban phải có ít nhất 2 ký tự"),
  code: z.string().min(2, "Mã phòng ban phải có ít nhất 2 ký tự").max(10, "Mã phòng ban không được quá 10 ký tự"),
  description: z.string().optional(),
  maxMembers: z.number().min(1, "Số lượng thành viên tối đa phải lớn hơn 0").default(100),
  defaultPermissions: z.array(z.string()).default([]),
});

type DepartmentFormData = z.infer<typeof departmentSchema>;

interface DepartmentFormProps {
  onSubmit: (data: DepartmentFormData) => Promise<void>;
  initialData?: Partial<DepartmentFormData>;
  isEditing?: boolean;
}

export default function DepartmentForm({ onSubmit, initialData, isEditing = false }: DepartmentFormProps) {
  const [loading, setLoading] = useState(false);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(
    initialData?.defaultPermissions || []
  );

  const form = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: initialData?.name || "",
      code: initialData?.code || "",
      description: initialData?.description || "",
      maxMembers: initialData?.maxMembers || 100,
      defaultPermissions: initialData?.defaultPermissions || [],
    },
  });

  const handleSubmit = async (data: DepartmentFormData) => {
    setLoading(true);
    try {
      await onSubmit({
        ...data,
        defaultPermissions: selectedPermissions,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permissionId)
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  // Group permissions by category
  const groupedPermissions = departmentPermissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, typeof departmentPermissions>);

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">
          {isEditing ? "Chỉnh Sửa Phòng Ban" : "Thêm Phòng Ban Mới"}
        </h2>
        <p className="text-gray-600 mt-1">
          {isEditing ? "Cập nhật thông tin phòng ban" : "Tạo phòng ban mới với các quyền mặc định"}
        </p>
      </div>

      <form onSubmit={form.handleSubmit(handleSubmit)} className="p-6 space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tên Phòng Ban *
            </label>
            <input
              type="text"
              {...form.register("name")}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Nhập tên phòng ban"
            />
            {form.formState.errors.name && (
              <p className="text-red-500 text-sm mt-1">{form.formState.errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mã Phòng Ban *
            </label>
            <input
              type="text"
              {...form.register("code")}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 uppercase"
              placeholder="VD: IT, HR, SALES"
              style={{ textTransform: 'uppercase' }}
              disabled={isEditing} // Code shouldn't be changed after creation
            />
            {form.formState.errors.code && (
              <p className="text-red-500 text-sm mt-1">{form.formState.errors.code.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Mô Tả
          </label>
          <textarea
            {...form.register("description")}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Mô tả về phòng ban và chức năng"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Số Lượng Thành Viên Tối Đa
          </label>
          <input
            type="number"
            {...form.register("maxMembers", { valueAsNumber: true })}
            min="1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {form.formState.errors.maxMembers && (
            <p className="text-red-500 text-sm mt-1">{form.formState.errors.maxMembers.message}</p>
          )}
        </div>

        {/* Permissions */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-4">
            Quyền Mặc Định
          </label>
          <p className="text-sm text-gray-600 mb-4">
            Các quyền này sẽ được tự động cấp cho tất cả thành viên mới của phòng ban
          </p>
          
          <div className="space-y-6">
            {Object.entries(groupedPermissions).map(([category, permissions]) => (
              <div key={category} className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">{category}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {permissions.map((permission) => (
                    <label key={permission.id} className="flex items-start space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedPermissions.includes(permission.id)}
                        onChange={() => handlePermissionToggle(permission.id)}
                        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div className="flex-1">
                        <span className="text-sm font-medium text-gray-900">
                          {permission.label}
                        </span>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => window.history.back()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <X size={16} className="mr-2" />
            Hủy
          </button>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Save size={16} className="mr-2" />
            {loading ? "Đang lưu..." : (isEditing ? "Cập Nhật" : "Tạo Phòng Ban")}
          </button>
        </div>
      </form>
    </div>
  );
}
