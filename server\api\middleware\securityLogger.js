const fs = require('fs');
const path = require('path');

/**
 * Security logging middleware for monitoring suspicious activities
 */

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const securityLogPath = path.join(logsDir, 'security.log');

// Security event types
const SECURITY_EVENTS = {
  LOGIN_ATTEMPT: 'LOGIN_ATTEMPT',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  VALIDATION_FAILURE: 'VALIDATION_FAILURE',
  UNAUTHORIZED_ACCESS: 'UNAUTHORIZED_ACCESS',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_HIT: 'RATE_LIMIT_HIT',
  FILE_UPLOAD: 'FILE_UPLOAD',
  ADMIN_ACTION: 'ADMIN_ACTION',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
  TWO_FACTOR_ATTEMPT: 'TWO_FACTOR_ATTEMPT',
  ACCOUNT_CREATION: 'ACCOUNT_CREATION'
};

// Write security log entry
const writeSecurityLog = (event, details) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    event,
    ...details
  };
  
  const logLine = JSON.stringify(logEntry) + '\n';
  
  fs.appendFile(securityLogPath, logLine, (err) => {
    if (err) {
      console.error('Failed to write security log:', err);
    }
  });
};

// Extract client information from request
const getClientInfo = (req) => {
  return {
    ip: req.ip || req.connection.remoteAddress || req.socket.remoteAddress,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    origin: req.get('Origin'),
    forwarded: req.get('X-Forwarded-For')
  };
};

// Security logging middleware
const securityLogger = {
  // Log login attempts
  logLoginAttempt: (req, success = false, userId = null, reason = null) => {
    writeSecurityLog(success ? SECURITY_EVENTS.LOGIN_SUCCESS : SECURITY_EVENTS.LOGIN_FAILURE, {
      userId,
      email: req.body.email,
      reason,
      clientInfo: getClientInfo(req)
    });
  },

  // Log validation failures
  logValidationFailure: (req, field, error) => {
    writeSecurityLog(SECURITY_EVENTS.VALIDATION_FAILURE, {
      field,
      error,
      path: req.path,
      method: req.method,
      body: JSON.stringify(req.body).substring(0, 500), // Limit logged data
      clientInfo: getClientInfo(req)
    });
  },

  // Log unauthorized access attempts
  logUnauthorizedAccess: (req, reason = 'Invalid token') => {
    writeSecurityLog(SECURITY_EVENTS.UNAUTHORIZED_ACCESS, {
      path: req.path,
      method: req.method,
      reason,
      clientInfo: getClientInfo(req)
    });
  },

  // Log suspicious activities
  logSuspiciousActivity: (req, activity, details = {}) => {
    writeSecurityLog(SECURITY_EVENTS.SUSPICIOUS_ACTIVITY, {
      activity,
      path: req.path,
      method: req.method,
      details,
      clientInfo: getClientInfo(req)
    });
  },

  // Log rate limit hits
  logRateLimit: (req, limit, windowMs) => {
    writeSecurityLog(SECURITY_EVENTS.RATE_LIMIT_HIT, {
      path: req.path,
      method: req.method,
      limit,
      windowMs,
      clientInfo: getClientInfo(req)
    });
  },

  // Log file uploads
  logFileUpload: (req, filename, filesize, mimetype, success = true, error = null) => {
    writeSecurityLog(SECURITY_EVENTS.FILE_UPLOAD, {
      filename,
      filesize,
      mimetype,
      success,
      error,
      path: req.path,
      userId: req.user?.id,
      clientInfo: getClientInfo(req)
    });
  },

  // Log admin actions
  logAdminAction: (req, action, targetUserId = null, details = {}) => {
    writeSecurityLog(SECURITY_EVENTS.ADMIN_ACTION, {
      action,
      adminUserId: req.user?.id,
      targetUserId,
      details,
      path: req.path,
      method: req.method,
      clientInfo: getClientInfo(req)
    });
  },

  // Log password changes
  logPasswordChange: (req, userId, success = true, reason = null) => {
    writeSecurityLog(SECURITY_EVENTS.PASSWORD_CHANGE, {
      userId,
      success,
      reason,
      clientInfo: getClientInfo(req)
    });
  },

  // Log 2FA attempts
  logTwoFactorAttempt: (req, userId, success = false, method = 'unknown') => {
    writeSecurityLog(SECURITY_EVENTS.TWO_FACTOR_ATTEMPT, {
      userId,
      success,
      method, // 'sms', 'app', 'email'
      clientInfo: getClientInfo(req)
    });
  },

  // Log account creation
  logAccountCreation: (req, userId, email, success = true, reason = null) => {
    writeSecurityLog(SECURITY_EVENTS.ACCOUNT_CREATION, {
      userId,
      email,
      success,
      reason,
      clientInfo: getClientInfo(req)
    });
  }
};

// Middleware to enhance input validation with security logging
const securityValidationMiddleware = (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    // Check if this is a validation error response
    if (data && data.errors && data.message === 'Validation failed') {
      // Log validation failure
      const firstError = data.errors[0];
      securityLogger.logValidationFailure(req, firstError.field, firstError.message);
      
      // Check for suspicious patterns
      const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /eval\s*\(/i,
        /document\./i,
        /window\./i
      ];
      
      const bodyString = JSON.stringify(req.body);
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(bodyString)) {
          securityLogger.logSuspiciousActivity(req, 'XSS_ATTEMPT', {
            pattern: pattern.toString(),
            field: firstError.field
          });
          break;
        }
      }
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

module.exports = {
  securityLogger,
  securityValidationMiddleware,
  SECURITY_EVENTS
};
