const mongoose = require("mongoose");
const User = require("../models/user");
const Department = require("../models/department");
require("dotenv").config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

const testDepartmentSystem = async () => {
  try {
    console.log("🧪 Testing Department-based System...");

    // Test 1: Check if departments exist
    console.log("\n📁 Test 1: Checking departments...");
    const departments = await Department.find({ isActive: true });
    console.log(`Found ${departments.length} active departments:`);
    departments.forEach(dept => {
      console.log(`  - ${dept.name} (${dept.code}): ${dept.defaultPermissions.length} default permissions`);
    });

    // Test 2: Check system admins
    console.log("\n👑 Test 2: Checking system admins...");
    const systemAdmins = await User.find({ systemRole: 'admin' });
    console.log(`Found ${systemAdmins.length} system admins:`);
    systemAdmins.forEach(admin => {
      console.log(`  - ${admin.email} (${admin.username})`);
    });

    // Test 3: Check department admins
    console.log("\n🏢 Test 3: Checking department admins...");
    const departmentAdmins = await User.find({ isDepartmentAdmin: true }).populate('department');
    console.log(`Found ${departmentAdmins.length} department admins:`);
    departmentAdmins.forEach(admin => {
      console.log(`  - ${admin.email} manages ${admin.department?.name || 'Unknown Department'}`);
    });

    // Test 4: Check users by department
    console.log("\n👥 Test 4: Checking users by department...");
    for (const dept of departments) {
      const users = await User.find({ department: dept._id });
      const adminUser = users.find(u => u.isDepartmentAdmin);
      console.log(`${dept.name}: ${users.length} users${adminUser ? `, Admin: ${adminUser.email}` : ', No admin'}`);
    }

    // Test 5: Check users without departments
    console.log("\n❓ Test 5: Checking users without departments...");
    const usersWithoutDept = await User.find({ 
      department: null, 
      systemRole: { $ne: 'admin' } 
    });
    console.log(`Found ${usersWithoutDept.length} users without departments:`);
    usersWithoutDept.forEach(user => {
      console.log(`  - ${user.email} (legacy rule: ${user.rule})`);
    });

    // Test 6: Permission validation
    console.log("\n🔐 Test 6: Testing permission system...");
    
    // Test system admin permissions
    const testSystemAdmin = systemAdmins[0];
    if (testSystemAdmin) {
      console.log(`Testing system admin permissions for ${testSystemAdmin.email}:`);
      console.log(`  - isSystemAdmin(): ${testSystemAdmin.isSystemAdmin()}`);
      console.log(`  - hasPermission('system_admin_full_access'): ${testSystemAdmin.hasPermission('system_admin_full_access')}`);
      console.log(`  - hasPermission('dept_user_view'): ${testSystemAdmin.hasPermission('dept_user_view')}`);
    }

    // Test department admin permissions
    const testDeptAdmin = departmentAdmins[0];
    if (testDeptAdmin) {
      console.log(`Testing department admin permissions for ${testDeptAdmin.email}:`);
      console.log(`  - isDeptAdmin(): ${testDeptAdmin.isDeptAdmin()}`);
      console.log(`  - hasPermission('dept_user_view'): ${testDeptAdmin.hasPermission('dept_user_view')}`);
      console.log(`  - hasPermission('system_admin_full_access'): ${testDeptAdmin.hasPermission('system_admin_full_access')}`);
    }

    // Test 7: Department statistics
    console.log("\n📊 Test 7: Department statistics...");
    const totalUsers = await User.countDocuments();
    const usersInDepartments = await User.countDocuments({ department: { $ne: null } });
    const usersWithSystemRole = await User.countDocuments({ systemRole: 'admin' });
    
    console.log(`Total users: ${totalUsers}`);
    console.log(`Users in departments: ${usersInDepartments}`);
    console.log(`System admins: ${usersWithSystemRole}`);
    console.log(`Users without departments: ${totalUsers - usersInDepartments - usersWithSystemRole}`);

    // Test 8: Validate department constraints
    console.log("\n✅ Test 8: Validating department constraints...");
    
    let validationErrors = 0;
    
    // Check if department admins are in their departments
    for (const admin of departmentAdmins) {
      if (!admin.department) {
        console.log(`❌ Department admin ${admin.email} has no department assigned`);
        validationErrors++;
      }
    }
    
    // Check if departments have valid admins
    for (const dept of departments) {
      if (dept.departmentAdmin) {
        const admin = await User.findById(dept.departmentAdmin);
        if (!admin || !admin.isDepartmentAdmin || !admin.department?.equals(dept._id)) {
          console.log(`❌ Department ${dept.name} has invalid admin assignment`);
          validationErrors++;
        }
      }
    }
    
    // Check for system admins in departments
    const invalidSystemAdmins = await User.find({ 
      systemRole: 'admin', 
      department: { $ne: null } 
    });
    
    if (invalidSystemAdmins.length > 0) {
      console.log(`❌ Found ${invalidSystemAdmins.length} system admins with department assignments`);
      validationErrors++;
    }

    if (validationErrors === 0) {
      console.log("✅ All validation checks passed!");
    } else {
      console.log(`❌ Found ${validationErrors} validation errors`);
    }

    // Test 9: Performance check
    console.log("\n⚡ Test 9: Performance check...");
    const startTime = Date.now();
    
    await Promise.all([
      User.findByDepartment(departments[0]?._id),
      User.findDepartmentAdmins(),
      User.findSystemAdmins(),
      Department.findActiveWithMembers()
    ]);
    
    const endTime = Date.now();
    console.log(`Query performance: ${endTime - startTime}ms`);

    console.log("\n🎉 Department system testing completed!");
    
    return {
      departments: departments.length,
      systemAdmins: systemAdmins.length,
      departmentAdmins: departmentAdmins.length,
      totalUsers,
      usersInDepartments,
      validationErrors
    };
    
  } catch (error) {
    console.error("❌ Testing failed:", error);
    throw error;
  }
};

// Run test if this script is executed directly
if (require.main === module) {
  testDepartmentSystem()
    .then((results) => {
      console.log("\n📋 Test Summary:", results);
      process.exit(0);
    })
    .catch((error) => {
      console.error("Testing failed:", error);
      process.exit(1);
    });
}

module.exports = testDepartmentSystem;
