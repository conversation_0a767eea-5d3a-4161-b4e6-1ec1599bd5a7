"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { toast } from "react-toastify";
import Link from "next/link";
import { ArrowLeft } from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";
import DepartmentDashboard from "@/components/Department/Dashboard";
import PermissionGuard from "@/components/PermissionGuard";

export default function DepartmentDashboardPage() {
  const params = useParams();
  const departmentId = params.id as string;
  const [department, setDepartment] = useState<Department | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDepartment = async () => {
      try {
        const sessionToken = localStorage.getItem("sessionToken") || "";
        const response = await departmentApiRequest.getDepartment(departmentId, sessionToken);
        
        if (response && response.payload && response.payload.success) {
          setDepartment(response.payload.department);
        } else {
          toast.error("Không thể tải thông tin phòng ban");
        }
      } catch (error: any) {
        console.error("Error fetching department:", error);
        toast.error("Có lỗi xảy ra khi tải thông tin phòng ban");
      } finally {
        setLoading(false);
      }
    };

    if (departmentId) {
      fetchDepartment();
    }
  }, [departmentId]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-gray-600">Đang tải...</div>
        </div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Không tìm thấy phòng ban</h1>
          <Link
            href="/dashboard/departments"
            className="inline-flex items-center text-blue-600 hover:text-blue-800"
          >
            <ArrowLeft size={20} className="mr-2" />
            Quay lại danh sách phòng ban
          </Link>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard 
      requiredPermissions={["system_departments_manage", "dept_dashboard_view"]} 
      requireAll={false}
      fallbackPath="/dashboard"
    >
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/departments/${departmentId}`}
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} className="mr-2" />
              Quay lại
            </Link>
          </div>
        </div>

        {/* Dashboard */}
        <DepartmentDashboard 
          departmentId={departmentId} 
          department={department}
        />
      </div>
    </PermissionGuard>
  );
}
