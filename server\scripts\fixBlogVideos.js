const mongoose = require('mongoose');
const Post = require('../api/models/post');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/blog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Fix blog posts with video field but no video in desc
const fixBlogVideos = async () => {
  try {
    console.log('🔧 Fixing blog posts with videos...');
    
    const posts = await Post.find({
      video: { $exists: true, $ne: [] },
      $or: [
        { desc: { $not: /<video/ } },
        { desc: { $exists: false } },
        { desc: "" }
      ]
    });
    
    console.log(`Found ${posts.length} posts with videos to fix`);
    let fixedCount = 0;
    
    for (const post of posts) {
      console.log(`\n📝 Processing post: ${post.title}`);
      console.log(`Post ID: ${post._id}`);
      console.log(`Video field:`, post.video);
      console.log(`Current desc length:`, post.desc?.length || 0);
      
      if (post.video && post.video.length > 0) {
        // Get video URL - handle different formats
        let videoUrl;
        if (typeof post.video === 'string') {
          videoUrl = post.video;
        } else if (Array.isArray(post.video)) {
          const firstVideo = post.video[0];
          if (typeof firstVideo === 'string') {
            videoUrl = firstVideo;
          } else if (typeof firstVideo === 'object' && firstVideo['0']) {
            videoUrl = firstVideo['0'];
          } else {
            videoUrl = Object.values(firstVideo)[0];
          }
        }
        console.log(`Video URL: ${videoUrl}`);
        
        // Create video HTML
        const videoHtml = `<video controls preload="metadata" style="width: 100%; max-width: 100%; height: auto;" src="${videoUrl}"></video>`;
        
        // Update desc with video
        const currentDesc = post.desc || "";
        const updatedDesc = currentDesc + "\n" + videoHtml;
        
        post.desc = updatedDesc;
        await post.save();
        
        console.log(`✅ Fixed post: ${post.title}`);
        console.log(`New desc length: ${updatedDesc.length}`);
        fixedCount++;
      }
    }
    
    console.log(`\n✅ Fixed ${fixedCount} blog posts with videos`);
  } catch (error) {
    console.error('❌ Error fixing blog videos:', error);
  }
};

// List all posts with videos for debugging
const listPostsWithVideos = async () => {
  try {
    console.log('📋 Listing all posts with videos...');
    
    const posts = await Post.find({
      video: { $exists: true, $ne: [] }
    }).select('_id title video desc');
    
    console.log(`Found ${posts.length} posts with videos:`);
    
    posts.forEach((post, index) => {
      console.log(`\n${index + 1}. ${post.title}`);
      console.log(`   ID: ${post._id}`);
      console.log(`   Video: ${JSON.stringify(post.video)}`);
      console.log(`   Desc length: ${post.desc?.length || 0}`);
      console.log(`   Has video in desc: ${post.desc?.includes('<video') || false}`);
    });
  } catch (error) {
    console.error('❌ Error listing posts:', error);
  }
};

// Main function
const main = async () => {
  console.log('🚀 Starting blog video fix script...');
  
  await connectDB();
  
  // List posts first
  await listPostsWithVideos();
  
  console.log('\n' + '='.repeat(50));
  
  // Fix posts
  await fixBlogVideos();
  
  console.log('\n✅ Blog video fix completed!');
  process.exit(0);
};

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { fixBlogVideos, listPostsWithVideos };
