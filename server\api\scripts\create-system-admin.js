const mongoose = require("mongoose");
const User = require("../models/user");
require("dotenv").config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

const createSystemAdmin = async (email) => {
  try {
    console.log(`🔧 Creating system admin for: ${email}`);

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    
    if (!user) {
      console.error(`❌ User with email ${email} not found`);
      return false;
    }

    console.log(`📧 Found user: ${user.username} (${user.email})`);

    // Update user to system admin
    const updateData = {
      systemRole: 'admin',
      department: null,
      isDepartmentAdmin: false,
      permissions: [
        'system_admin_full_access',
        'system_departments_manage',
        'system_users_manage',
        'system_settings_manage',
        'system_analytics_view',
        // Keep legacy permissions for backward compatibility
        'user_view',
        'user_add',
        'user_edit',
        'user_delete',
        'user_import_csv',
        'file_view',
        'file_upload',
        'file_delete',
        'system_settings_view',
        'system_settings_edit',
        'analytics_view',
        'permissions_manage'
      ]
    };

    await User.findByIdAndUpdate(user._id, updateData);

    console.log(`✅ Successfully promoted ${user.email} to System Administrator`);
    console.log(`🔑 Granted permissions:`, updateData.permissions);

    // Verify the update
    const updatedUser = await User.findById(user._id);
    console.log(`\n📋 Verification:`);
    console.log(`   System Role: ${updatedUser.systemRole}`);
    console.log(`   Department: ${updatedUser.department || 'None (System Admin)'}`);
    console.log(`   Is Dept Admin: ${updatedUser.isDepartmentAdmin}`);
    console.log(`   Permissions Count: ${updatedUser.permissions.length}`);

    return true;

  } catch (error) {
    console.error("❌ Error creating system admin:", error);
    return false;
  }
};

// Get email from command line arguments
const email = process.argv[2];

if (!email) {
  console.log("📝 Usage: node create-system-admin.js <email>");
  console.log("📝 Example: node create-system-admin.js <EMAIL>");
  process.exit(1);
}

// Run the script
createSystemAdmin(email)
  .then((success) => {
    if (success) {
      console.log("\n🎉 System admin creation completed successfully!");
    } else {
      console.log("\n❌ System admin creation failed!");
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error("Script failed:", error);
    process.exit(1);
  });
