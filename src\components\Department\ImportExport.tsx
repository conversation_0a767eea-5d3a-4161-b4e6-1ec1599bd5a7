"use client";

import { useState } from "react";
import { toast } from "react-toastify";
import {
  Download,
  Upload,
  FileText,
  Users,
  Volume2, // Thay thế Megaphone
  BarChart, // Thay thế BarChart3
  Database,
  Calendar,
  Settings,
  CheckCircle,
  AlertCircle
} from "react-feather";
import departmentImportExportApiRequest, { ExportOptions } from "@/apiRequests/departmentImportExport";

interface ImportExportProps {
  departmentId: string;
  departmentName: string;
}

export default function ImportExport({ departmentId, departmentName }: ImportExportProps) {
  const [exporting, setExporting] = useState<string | null>(null);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'excel',
    includeMembers: true,
    includeFiles: true,
    includeAnnouncements: true,
    includeReports: true
  });

  const handleExport = async (type: 'members' | 'reports' | 'data') => {
    try {
      setExporting(type);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      
      let response;
      let defaultFilename;
      
      switch (type) {
        case 'members':
          response = await departmentImportExportApiRequest.exportMembers(departmentId, exportOptions, sessionToken);
          defaultFilename = `members-${departmentId}-${Date.now()}.${exportOptions.format === 'csv' ? 'csv' : 'xlsx'}`;
          break;
        case 'reports':
          response = await departmentImportExportApiRequest.exportReports(departmentId, exportOptions, sessionToken);
          defaultFilename = `reports-${departmentId}-${Date.now()}.xlsx`;
          break;
        case 'data':
          response = await departmentImportExportApiRequest.exportDepartmentData(departmentId, exportOptions, sessionToken);
          defaultFilename = `department-data-${departmentId}-${Date.now()}.xlsx`;
          break;
        default:
          throw new Error('Invalid export type');
      }

      if (response && response.payload) {
        const filename = departmentImportExportApiRequest.getFilenameFromResponse(response) || defaultFilename;
        departmentImportExportApiRequest.downloadBlob(response.payload, filename);
        toast.success("Xuất dữ liệu thành công");
      } else {
        toast.error("Không thể xuất dữ liệu");
      }
      
    } catch (error: any) {
      console.error("Error exporting data:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi xuất dữ liệu";
      toast.error(errorMessage);
    } finally {
      setExporting(null);
    }
  };

  const exportCards = [
    {
      id: 'members',
      title: 'Xuất danh sách thành viên',
      description: 'Xuất thông tin tất cả thành viên trong phòng ban',
      icon: Users,
      color: 'blue',
      formats: ['excel', 'csv'],
      action: () => handleExport('members')
    },
    {
      id: 'reports',
      title: 'Xuất báo cáo',
      description: 'Xuất tất cả báo cáo và thống kê của phòng ban',
      icon: BarChart,
      color: 'green',
      formats: ['excel'],
      action: () => handleExport('reports')
    },
    {
      id: 'data',
      title: 'Xuất dữ liệu tổng hợp',
      description: 'Xuất toàn bộ dữ liệu phòng ban (thành viên, file, thông báo, báo cáo)',
      icon: Database,
      color: 'purple',
      formats: ['excel'],
      action: () => handleExport('data')
    }
  ];

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          icon: 'text-blue-600',
          button: 'bg-blue-600 hover:bg-blue-700'
        };
      case 'green':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          icon: 'text-green-600',
          button: 'bg-green-600 hover:bg-green-700'
        };
      case 'purple':
        return {
          bg: 'bg-purple-50',
          border: 'border-purple-200',
          icon: 'text-purple-600',
          button: 'bg-purple-600 hover:bg-purple-700'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          icon: 'text-gray-600',
          button: 'bg-gray-600 hover:bg-gray-700'
        };
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Import/Export dữ liệu</h2>
        <p className="text-gray-600">Xuất và nhập dữ liệu phòng ban {departmentName}</p>
      </div>

      {/* Export Options */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Tùy chọn xuất dữ liệu</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Format Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Định dạng file
            </label>
            <select
              value={exportOptions.format}
              onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as 'excel' | 'csv' }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="excel">Excel (.xlsx)</option>
              <option value="csv">CSV (.csv)</option>
            </select>
          </div>

          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Khoảng thời gian (tùy chọn)
            </label>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="date"
                value={exportOptions.startDate || ''}
                onChange={(e) => setExportOptions(prev => ({ ...prev, startDate: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Từ ngày"
              />
              <input
                type="date"
                value={exportOptions.endDate || ''}
                onChange={(e) => setExportOptions(prev => ({ ...prev, endDate: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Đến ngày"
              />
            </div>
          </div>
        </div>

        {/* Include Options */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Dữ liệu cần xuất
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={exportOptions.includeMembers}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includeMembers: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Thành viên</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={exportOptions.includeFiles}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includeFiles: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">File</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={exportOptions.includeAnnouncements}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includeAnnouncements: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Thông báo</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={exportOptions.includeReports}
                onChange={(e) => setExportOptions(prev => ({ ...prev, includeReports: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Báo cáo</span>
            </label>
          </div>
        </div>
      </div>

      {/* Export Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {exportCards.map((card) => {
          const Icon = card.icon;
          const colors = getColorClasses(card.color);
          const isExporting = exporting === card.id;
          
          return (
            <div
              key={card.id}
              className={`border rounded-lg p-6 ${colors.bg} ${colors.border}`}
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className={`p-2 rounded-lg bg-white`}>
                  <Icon size={24} className={colors.icon} />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{card.title}</h3>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mb-4">{card.description}</p>
              
              <div className="flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  Định dạng: {card.formats.join(', ').toUpperCase()}
                </div>
                <button
                  onClick={card.action}
                  disabled={isExporting}
                  className={`inline-flex items-center px-4 py-2 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${colors.button}`}
                >
                  {isExporting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Đang xuất...
                    </>
                  ) : (
                    <>
                      <Download size={16} className="mr-2" />
                      Xuất dữ liệu
                    </>
                  )}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Import Section (Placeholder) */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Upload size={24} className="text-gray-600" />
          <div>
            <h3 className="text-lg font-medium text-gray-900">Nhập dữ liệu</h3>
            <p className="text-sm text-gray-600">Nhập dữ liệu từ file Excel hoặc CSV</p>
          </div>
        </div>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <Upload size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600 mb-2">Tính năng nhập dữ liệu</p>
          <p className="text-sm text-gray-500">Sẽ được phát triển trong phiên bản tiếp theo</p>
        </div>
      </div>

      {/* Help Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertCircle size={20} className="text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 mb-1">Lưu ý khi xuất dữ liệu</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• File Excel hỗ trợ nhiều sheet và định dạng phong phú hơn</li>
              <li>• File CSV phù hợp để import vào các hệ thống khác</li>
              <li>• Dữ liệu được xuất theo quyền truy cập của bạn</li>
              <li>• Chọn khoảng thời gian để lọc dữ liệu theo ngày tạo</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
