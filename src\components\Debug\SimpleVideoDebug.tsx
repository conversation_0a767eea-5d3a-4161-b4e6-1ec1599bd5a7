"use client";

import { useEffect, useState } from 'react';

const SimpleVideoDebug = () => {
  const [videoCount, setVideoCount] = useState(0);
  const [proseMirrorContent, setProseMirrorContent] = useState('');

  const checkVideos = () => {
    const videos = document.querySelectorAll('video');
    const prosemirror = document.querySelector('.ProseMirror');
    
    setVideoCount(videos.length);
    setProseMirrorContent(prosemirror?.innerHTML.substring(0, 500) || 'No ProseMirror found');
    
    console.log('Videos found:', videos.length);
    console.log('ProseMirror content:', prosemirror?.innerHTML);
  };

  useEffect(() => {
    const interval = setInterval(checkVideos, 1000);
    checkVideos(); // Initial check
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-2 bg-yellow-100 rounded mb-2 text-sm">
      <strong>Quick Video Check:</strong> {videoCount} videos found
      <br />
      <strong>ProseMirror:</strong> {proseMirrorContent.substring(0, 100)}...
      <button 
        onClick={checkVideos}
        className="ml-2 px-2 py-1 bg-blue-500 text-white rounded text-xs"
      >
        Refresh
      </button>
    </div>
  );
};

export default SimpleVideoDebug;
