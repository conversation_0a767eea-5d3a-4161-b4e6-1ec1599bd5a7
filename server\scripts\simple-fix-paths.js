const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function fixPaths() {
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('files');
    
    // Find files without relativePath
    const files = await collection.find({ relativePath: { $exists: false } }).toArray();
    console.log('Found', files.length, 'files to fix');
    
    for (const file of files) {
      if (file.url) {
        let relativePath = file.url.replace(/^\/+/, ''); // Remove leading slashes
        relativePath = relativePath.replace(/^uploads\//, ''); // Remove uploads/ prefix
        
        console.log('Fixing:', file.filename);
        console.log('  URL:', file.url);
        console.log('  Setting relativePath:', relativePath);
        
        await collection.updateOne(
          { _id: file._id },
          { $set: { relativePath: relativePath } }
        );
      }
    }
    
    console.log('✅ All files fixed');
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

mongoose.connection.on('connected', () => {
  console.log('Connected to MongoDB');
  fixPaths();
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB error:', err);
  process.exit(1);
});
