"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { toast } from "react-toastify";
import Link from "next/link";
import { ArrowLeft, Edit, Users, Settings, Save } from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";
import DepartmentForm from "@/components/Department/DepartmentForm";
import PermissionGuard from "@/components/PermissionGuard";

export default function DepartmentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [department, setDepartment] = useState<Department | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);

  const departmentId = params.id as string;

  const fetchDepartment = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.getDepartment(departmentId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        setDepartment(response.payload.department);
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error: any) {
      console.error("Error fetching department:", error);
      toast.error("Có lỗi xảy ra khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
    }
  }, [departmentId]);

  const handleUpdate = async (data: any) => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.updateDepartment(departmentId, data, sessionToken);
      
      if (result.payload.success) {
        toast.success("Cập nhật phòng ban thành công!");
        setDepartment(result.payload.department);
        setEditing(false);
      } else {
        toast.error("Không thể cập nhật phòng ban. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error updating department:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi cập nhật phòng ban";
      toast.error(errorMessage);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-gray-600">Đang tải thông tin phòng ban...</div>
        </div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy phòng ban</h2>
            <p className="text-gray-600 mb-4">Phòng ban bạn đang tìm kiếm không tồn tại.</p>
            <Link
              href="/dashboard/departments"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft size={16} className="mr-2" />
              Quay lại danh sách
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermissions={["system_departments_manage"]} fallbackPath="/dashboard">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              href="/dashboard/departments"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} className="mr-2" />
              Quay lại
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{department.name}</h1>
              <p className="text-gray-600">Mã phòng ban: {department.code}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {!editing && (
              <>
                <button
                  onClick={() => setEditing(true)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Edit size={16} className="mr-2" />
                  Chỉnh sửa
                </button>
                <Link
                  href={`/dashboard/departments/${departmentId}/members`}
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Users size={16} className="mr-2" />
                  Quản lý thành viên
                </Link>
                <Link
                  href={`/dashboard/departments/${departmentId}/settings`}
                  className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <Settings size={16} className="mr-2" />
                  Cài đặt
                </Link>
              </>
            )}
          </div>
        </div>

        {/* Content */}
        {editing ? (
          <DepartmentForm
            onSubmit={handleUpdate}
            initialData={{
              name: department.name,
              description: department.description,
              defaultPermissions: department.defaultPermissions,
              maxMembers: department.maxMembers,
            }}
            isEditing={true}
          />
        ) : (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông tin cơ bản</h3>
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Tên phòng ban</dt>
                      <dd className="text-sm text-gray-900">{department.name}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Mã phòng ban</dt>
                      <dd className="text-sm text-gray-900">{department.code}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Mô tả</dt>
                      <dd className="text-sm text-gray-900">{department.description || "Không có mô tả"}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Số lượng thành viên tối đa</dt>
                      <dd className="text-sm text-gray-900">{department.maxMembers}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Trạng thái</dt>
                      <dd>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          department.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {department.isActive ? 'Hoạt động' : 'Tạm dừng'}
                        </span>
                      </dd>
                    </div>
                  </dl>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Quản lý</h3>
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Quản lý phòng ban</dt>
                      <dd className="text-sm text-gray-900">
                        {department.departmentAdmin ? (
                          <div>
                            <div className="font-medium">{department.departmentAdmin.username}</div>
                            <div className="text-gray-500">{department.departmentAdmin.email}</div>
                          </div>
                        ) : (
                          <span className="text-gray-400 italic">Chưa có quản lý</span>
                        )}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Số lượng thành viên hiện tại</dt>
                      <dd className="text-sm text-gray-900">{department.memberCount || 0}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Ngày tạo</dt>
                      <dd className="text-sm text-gray-900">
                        {new Date(department.createdAt).toLocaleDateString("vi-VN")}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Cập nhật lần cuối</dt>
                      <dd className="text-sm text-gray-900">
                        {new Date(department.updatedAt).toLocaleDateString("vi-VN")}
                      </dd>
                    </div>
                  </dl>
                </div>
              </div>

              {/* Permissions */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quyền mặc định</h3>
                {department.defaultPermissions.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {department.defaultPermissions.map((permission) => (
                      <span
                        key={permission}
                        className="inline-flex px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">Chưa có quyền mặc định nào được cấu hình</p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </PermissionGuard>
  );
}
