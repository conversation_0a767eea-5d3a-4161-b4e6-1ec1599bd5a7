import http from "@/lib/http";

export interface DepartmentAnnouncement {
  _id: string;
  department: {
    _id: string;
    name: string;
    code: string;
  };
  title: string;
  content: string;
  summary?: string;
  type: "general" | "urgent" | "meeting" | "deadline" | "policy" | "event" | "training" | "maintenance";
  priority: "low" | "normal" | "high" | "urgent";
  status: "draft" | "published" | "archived" | "expired";
  publishedAt?: string;
  expiresAt?: string;
  scheduledFor?: string;
  author: {
    _id: string;
    username: string;
    email: string;
  };
  lastEditedBy?: {
    _id: string;
    username: string;
    email: string;
  };
  targetAudience: "all" | "admins" | "members" | "specific";
  specificTargets: Array<{
    _id: string;
    username: string;
    email: string;
  }>;
  attachments: Array<{
    fileName: string;
    originalName: string;
    fileSize: number;
    mimeType: string;
    filePath: string;
    uploadedAt: string;
  }>;
  views: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    viewedAt: string;
    ipAddress?: string;
    userAgent?: string;
  }>;
  likes: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    likedAt: string;
  }>;
  comments: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    content: string;
    createdAt: string;
    editedAt?: string;
    isEdited: boolean;
  }>;
  allowComments: boolean;
  allowLikes: boolean;
  requireAcknowledgment: boolean;
  acknowledgments: Array<{
    user: {
      _id: string;
      username: string;
      email: string;
    };
    acknowledgedAt: string;
    ipAddress?: string;
  }>;
  sendNotification: boolean;
  notificationSent: boolean;
  notificationSentAt?: string;
  tags: string[];
  slug?: string;
  metaDescription?: string;
  analytics: {
    totalViews: number;
    uniqueViews: number;
    totalLikes: number;
    totalComments: number;
    acknowledgmentRate: number;
  };
  createdAt: string;
  updatedAt: string;
  isActive?: boolean;
  isExpired?: boolean;
  timeUntilExpiry?: number;
}

export interface CreateAnnouncementRequest {
  title: string;
  content: string;
  summary?: string;
  type?: string;
  priority?: string;
  targetAudience?: string;
  specificTargets?: string[];
  expiresAt?: string;
  scheduledFor?: string;
  allowComments?: boolean;
  allowLikes?: boolean;
  requireAcknowledgment?: boolean;
  sendNotification?: boolean;
  tags?: string[];
}

export interface UpdateAnnouncementRequest {
  title?: string;
  content?: string;
  summary?: string;
  type?: string;
  priority?: string;
  targetAudience?: string;
  specificTargets?: string[];
  expiresAt?: string;
  scheduledFor?: string;
  allowComments?: boolean;
  allowLikes?: boolean;
  requireAcknowledgment?: boolean;
  tags?: string[];
  status?: string;
}

export interface AnnouncementListResponse {
  success: boolean;
  announcements: DepartmentAnnouncement[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export interface AnnouncementResponse {
  success: boolean;
  announcement: DepartmentAnnouncement;
  message?: string;
}

const departmentAnnouncementApiRequest = {
  // Get all announcements for a department
  getDepartmentAnnouncements: (
    departmentId: string,
    params: {
      page?: number;
      perPage?: number;
      status?: string;
      type?: string;
      priority?: string;
      tags?: string;
      activeOnly?: boolean;
    },
    sessionToken: string
  ) => {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });
    
    return http.get<AnnouncementListResponse>(
      `/api/administrator/departments/${departmentId}/announcements?${queryParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      }
    );
  },

  // Get single announcement
  getAnnouncement: (id: string, sessionToken: string) =>
    http.get<AnnouncementResponse>(`/api/administrator/departments/announcements/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Create new announcement
  createAnnouncement: (departmentId: string, data: CreateAnnouncementRequest, sessionToken: string) =>
    http.post<AnnouncementResponse>(`/api/administrator/departments/${departmentId}/announcements`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Update announcement
  updateAnnouncement: (id: string, data: UpdateAnnouncementRequest, sessionToken: string) =>
    http.put<AnnouncementResponse>(`/api/administrator/departments/announcements/${id}`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete announcement
  deleteAnnouncement: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(`/api/administrator/departments/announcements/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Like/Unlike announcement
  toggleLike: (id: string, sessionToken: string) =>
    http.post<{ success: boolean; message: string }>(`/api/administrator/departments/announcements/${id}/like`, {}, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Add comment to announcement
  addComment: (id: string, content: string, sessionToken: string) =>
    http.post<{ success: boolean; message: string; comments: DepartmentAnnouncement['comments'] }>(`/api/administrator/departments/announcements/${id}/comments`, { content }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Acknowledge announcement
  acknowledge: (id: string, sessionToken: string) =>
    http.post<{ success: boolean; message: string }>(`/api/administrator/departments/announcements/${id}/acknowledge`, {}, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get announcement analytics
  getAnalytics: (id: string, sessionToken: string) =>
    http.get<{ success: boolean; analytics: DepartmentAnnouncement['analytics'] }>(`/api/administrator/departments/announcements/${id}/analytics`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default departmentAnnouncementApiRequest;
