import envConfig from "@/config";

export function getImageUrl(path: string | undefined): string {
  if (!path) return ""; // Handle cases where the path is undefined

  // Clean up path - remove leading slash to avoid double slashes
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;

  // Fix any remaining server/uploads paths
  const fixedPath = cleanPath.replace('server/uploads/', 'uploads/');

  return `${envConfig.NEXT_PUBLIC_API_ENDPOINT}/${fixedPath}`;
}
