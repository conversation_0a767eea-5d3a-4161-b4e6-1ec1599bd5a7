const mongoose = require('mongoose');
require('dotenv').config();

mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function fixSettingLogo() {
  try {
    const db = mongoose.connection.db;
    
    // Get existing file
    const files = await db.collection('files').find({}).toArray();
    if (files.length === 0) {
      console.log('No files found');
      process.exit(1);
    }
    
    const existingFile = files[0];
    console.log('Using existing file:', existingFile.filename);
    console.log('File URL:', existingFile.url);
    
    // Update setting to use existing file
    const result = await db.collection('settings').updateMany(
      {},
      {
        $set: {
          'logo.path': existingFile.url
        }
      }
    );
    
    console.log('Updated', result.modifiedCount, 'settings');
    
    // Verify
    const settings = await db.collection('settings').find({}).toArray();
    settings.forEach(s => {
      console.log('Updated logo path:', s.logo?.path);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

mongoose.connection.on('connected', () => {
  console.log('Connected to MongoDB');
  fixSettingLogo();
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB error:', err);
  process.exit(1);
});
