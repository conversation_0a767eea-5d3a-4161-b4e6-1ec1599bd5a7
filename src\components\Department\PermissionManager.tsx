"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import {
  Shield,
  Users,
  FileText,
  Volume2, // Thay thế Megaphone
  BarChart, // Thay thế BarChart3
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Share,
  Download,
  MessageCircle,
  Zap,
  Save
} from "react-feather";

interface PermissionManagerProps {
  departmentId: string;
  userId?: string;
  userPermissions?: string[];
  onUpdate: (permissions: string[]) => void;
}

interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  icon: any;
  permissions: Permission[];
}

interface Permission {
  id: string;
  name: string;
  description: string;
  icon: any;
  level: 'basic' | 'advanced' | 'admin';
}

export default function PermissionManager({ 
  departmentId, 
  userId, 
  userPermissions = [], 
  onUpdate 
}: PermissionManagerProps) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(userPermissions);
  const [saving, setSaving] = useState(false);
  const [activeGroup, setActiveGroup] = useState('members');

  const permissionGroups: PermissionGroup[] = [
    {
      id: 'members',
      name: 'Quản lý thành viên',
      description: 'Quyền liên quan đến thành viên phòng ban',
      icon: Users,
      permissions: [
        {
          id: 'dept_members_view',
          name: 'Xem danh sách thành viên',
          description: 'Có thể xem thông tin các thành viên trong phòng ban',
          icon: Eye,
          level: 'basic'
        },
        {
          id: 'dept_members_invite',
          name: 'Mời thành viên mới',
          description: 'Có thể mời và thêm thành viên mới vào phòng ban',
          icon: Plus,
          level: 'advanced'
        },
        {
          id: 'dept_members_edit',
          name: 'Chỉnh sửa thông tin thành viên',
          description: 'Có thể chỉnh sửa thông tin và quyền của thành viên',
          icon: Edit,
          level: 'advanced'
        },
        {
          id: 'dept_members_remove',
          name: 'Xóa thành viên',
          description: 'Có thể xóa thành viên khỏi phòng ban',
          icon: Trash2,
          level: 'admin'
        }
      ]
    },
    {
      id: 'files',
      name: 'Quản lý file',
      description: 'Quyền liên quan đến file và tài liệu',
      icon: FileText,
      permissions: [
        {
          id: 'dept_files_view',
          name: 'Xem file',
          description: 'Có thể xem và tải xuống file trong phòng ban',
          icon: Eye,
          level: 'basic'
        },
        {
          id: 'dept_files_upload',
          name: 'Tải lên file',
          description: 'Có thể tải lên file mới vào phòng ban',
          icon: Plus,
          level: 'basic'
        },
        {
          id: 'dept_files_edit',
          name: 'Chỉnh sửa file',
          description: 'Có thể chỉnh sửa thông tin và metadata của file',
          icon: Edit,
          level: 'advanced'
        },
        {
          id: 'dept_files_delete',
          name: 'Xóa file',
          description: 'Có thể xóa file khỏi phòng ban',
          icon: Trash2,
          level: 'advanced'
        },
        {
          id: 'dept_files_share',
          name: 'Chia sẻ file',
          description: 'Có thể chia sẻ file với thành viên khác',
          icon: Share,
          level: 'basic'
        }
      ]
    },
    {
      id: 'announcements',
      name: 'Quản lý thông báo',
      description: 'Quyền liên quan đến thông báo và giao tiếp',
      icon: Volume2,
      permissions: [
        {
          id: 'dept_announcements_view',
          name: 'Xem thông báo',
          description: 'Có thể xem các thông báo trong phòng ban',
          icon: Eye,
          level: 'basic'
        },
        {
          id: 'dept_announcements_create',
          name: 'Tạo thông báo',
          description: 'Có thể tạo thông báo mới cho phòng ban',
          icon: Plus,
          level: 'advanced'
        },
        {
          id: 'dept_announcements_edit',
          name: 'Chỉnh sửa thông báo',
          description: 'Có thể chỉnh sửa thông báo đã tạo',
          icon: Edit,
          level: 'advanced'
        },
        {
          id: 'dept_announcements_delete',
          name: 'Xóa thông báo',
          description: 'Có thể xóa thông báo khỏi phòng ban',
          icon: Trash2,
          level: 'admin'
        },
        {
          id: 'dept_announcements_comment',
          name: 'Bình luận thông báo',
          description: 'Có thể bình luận và tương tác với thông báo',
          icon: MessageCircle,
          level: 'basic'
        }
      ]
    },
    {
      id: 'reports',
      name: 'Quản lý báo cáo',
      description: 'Quyền liên quan đến báo cáo và thống kê',
      icon: BarChart,
      permissions: [
        {
          id: 'dept_reports_view',
          name: 'Xem báo cáo',
          description: 'Có thể xem các báo cáo của phòng ban',
          icon: Eye,
          level: 'basic'
        },
        {
          id: 'dept_reports_create',
          name: 'Tạo báo cáo',
          description: 'Có thể tạo báo cáo mới cho phòng ban',
          icon: Plus,
          level: 'advanced'
        },
        {
          id: 'dept_reports_edit',
          name: 'Chỉnh sửa báo cáo',
          description: 'Có thể chỉnh sửa báo cáo đã tạo',
          icon: Edit,
          level: 'advanced'
        },
        {
          id: 'dept_reports_delete',
          name: 'Xóa báo cáo',
          description: 'Có thể xóa báo cáo khỏi phòng ban',
          icon: Trash2,
          level: 'admin'
        },
        {
          id: 'dept_reports_export',
          name: 'Xuất báo cáo',
          description: 'Có thể xuất báo cáo ra các định dạng khác',
          icon: Download,
          level: 'basic'
        }
      ]
    },
    {
      id: 'analytics',
      name: 'Thống kê & Dashboard',
      description: 'Quyền liên quan đến dashboard và phân tích',
      icon: BarChart,
      permissions: [
        {
          id: 'dept_dashboard_view',
          name: 'Xem dashboard',
          description: 'Có thể xem dashboard tổng quan của phòng ban',
          icon: Eye,
          level: 'basic'
        },
        {
          id: 'dept_analytics_view',
          name: 'Xem thống kê chi tiết',
          description: 'Có thể xem các thống kê và phân tích chi tiết',
          icon: BarChart,
          level: 'advanced'
        },
        {
          id: 'dept_analytics_export',
          name: 'Xuất dữ liệu thống kê',
          description: 'Có thể xuất dữ liệu thống kê ra file',
          icon: Download,
          level: 'advanced'
        }
      ]
    },
    {
      id: 'settings',
      name: 'Cài đặt & Quản trị',
      description: 'Quyền liên quan đến cài đặt và quản trị phòng ban',
      icon: Settings,
      permissions: [
        {
          id: 'dept_settings_view',
          name: 'Xem cài đặt',
          description: 'Có thể xem các cài đặt của phòng ban',
          icon: Eye,
          level: 'advanced'
        },
        {
          id: 'dept_settings_edit',
          name: 'Chỉnh sửa cài đặt',
          description: 'Có thể chỉnh sửa cài đặt cơ bản của phòng ban',
          icon: Edit,
          level: 'admin'
        },
        {
          id: 'dept_workflow_manage',
          name: 'Quản lý quy trình',
          description: 'Có thể quản lý quy trình làm việc và phê duyệt',
          icon: Zap,
          level: 'admin'
        },
        {
          id: 'dept_permissions_manage',
          name: 'Quản lý phân quyền',
          description: 'Có thể quản lý phân quyền cho thành viên',
          icon: Shield,
          level: 'admin'
        },
        {
          id: 'dept_integrations_view',
          name: 'Xem tích hợp',
          description: 'Có thể xem các tích hợp bên ngoài',
          icon: Eye,
          level: 'advanced'
        },
        {
          id: 'dept_integrations_manage',
          name: 'Quản lý tích hợp',
          description: 'Có thể quản lý tích hợp với các dịch vụ bên ngoài',
          icon: Zap,
          level: 'admin'
        }
      ]
    }
  ];

  useEffect(() => {
    setSelectedPermissions(userPermissions);
  }, [userPermissions]);

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => {
      if (prev.includes(permissionId)) {
        return prev.filter(p => p !== permissionId);
      } else {
        return [...prev, permissionId];
      }
    });
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      await onUpdate(selectedPermissions);
      toast.success("Cập nhật phân quyền thành công");
    } catch (error) {
      console.error("Error updating permissions:", error);
      toast.error("Có lỗi xảy ra khi cập nhật phân quyền");
    } finally {
      setSaving(false);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'basic':
        return 'bg-green-100 text-green-800';
      case 'advanced':
        return 'bg-yellow-100 text-yellow-800';
      case 'admin':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelName = (level: string) => {
    switch (level) {
      case 'basic':
        return 'Cơ bản';
      case 'advanced':
        return 'Nâng cao';
      case 'admin':
        return 'Quản trị';
      default:
        return level;
    }
  };

  const activeGroupData = permissionGroups.find(g => g.id === activeGroup);

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Shield size={24} className="text-gray-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Quản lý phân quyền</h2>
              <p className="text-sm text-gray-600">Cấu hình quyền truy cập chi tiết cho thành viên</p>
            </div>
          </div>
          <button
            onClick={handleSave}
            disabled={saving}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <Save size={16} className="mr-2" />
            {saving ? 'Đang lưu...' : 'Lưu phân quyền'}
          </button>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 border-r border-gray-200">
          <nav className="p-4 space-y-2">
            {permissionGroups.map((group) => {
              const Icon = group.icon;
              return (
                <button
                  key={group.id}
                  onClick={() => setActiveGroup(group.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeGroup === group.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon size={18} />
                  <div>
                    <div className="font-medium">{group.name}</div>
                    <div className="text-xs text-gray-500">{group.permissions.length} quyền</div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          {activeGroupData && (
            <div>
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900">{activeGroupData.name}</h3>
                <p className="text-sm text-gray-600">{activeGroupData.description}</p>
              </div>

              <div className="space-y-4">
                {activeGroupData.permissions.map((permission) => {
                  const Icon = permission.icon;
                  const isSelected = selectedPermissions.includes(permission.id);
                  
                  return (
                    <div
                      key={permission.id}
                      className={`border rounded-lg p-4 transition-colors ${
                        isSelected ? 'border-blue-200 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <label className="flex items-center mt-1">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => handlePermissionToggle(permission.id)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </label>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <Icon size={18} className="text-gray-600" />
                            <h4 className="font-medium text-gray-900">{permission.name}</h4>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getLevelColor(permission.level)}`}>
                              {getLevelName(permission.level)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{permission.description}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Summary */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Đã chọn {selectedPermissions.length} quyền trên tổng số{' '}
            {permissionGroups.reduce((total, group) => total + group.permissions.length, 0)} quyền có sẵn
          </div>
          <div className="flex items-center space-x-4 text-xs">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Cơ bản</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span>Nâng cao</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span>Quản trị</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
