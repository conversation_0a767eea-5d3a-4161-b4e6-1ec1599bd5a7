"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { toast } from "react-toastify";
import Link from "next/link";
import { ArrowLeft, Settings, Save, AlertTriangle } from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";
import PermissionGuard from "@/components/PermissionGuard";
import AdvancedSettings from "@/components/Department/AdvancedSettings";

export default function DepartmentSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const [department, setDepartment] = useState<Department | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    maxMembers: 50,
    isActive: true,
    defaultPermissions: [] as string[],
    allowSelfJoin: false,
    requireApproval: true,
    autoAssignPermissions: true,
  });

  const departmentId = params.id as string;

  const availablePermissions = [
    "dept_posts_view",
    "dept_posts_create", 
    "dept_posts_edit",
    "dept_posts_delete",
    "dept_files_view",
    "dept_files_upload",
    "dept_files_delete",
    "dept_analytics_view",
    "dept_members_view",
  ];

  const fetchDepartment = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.getDepartment(departmentId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        const dept = response.payload.department;
        setDepartment(dept);
        setFormData({
          maxMembers: dept.maxMembers || 50,
          isActive: dept.isActive,
          defaultPermissions: dept.defaultPermissions || [],
          allowSelfJoin: dept.allowSelfJoin || false,
          requireApproval: dept.requireApproval !== false,
          autoAssignPermissions: dept.autoAssignPermissions !== false,
        });
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error: any) {
      console.error("Error fetching department:", error);
      toast.error("Có lỗi xảy ra khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
    }
  }, [departmentId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.updateDepartment(departmentId, formData, sessionToken);
      
      if (result.payload.success) {
        toast.success("Cập nhật cài đặt phòng ban thành công!");
        setDepartment(result.payload.department);
      } else {
        toast.error("Không thể cập nhật cài đặt. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error updating department settings:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi cập nhật cài đặt";
      toast.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      defaultPermissions: checked
        ? [...prev.defaultPermissions, permission]
        : prev.defaultPermissions.filter(p => p !== permission)
    }));
  };

  const handleAdvancedSettingsUpdate = async (advancedSettings: any) => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";

      const response = await departmentApiRequest.updateDepartment(
        departmentId,
        {
          ...advancedSettings
        },
        sessionToken
      );

      if (response && response.payload && response.payload.success) {
        // Update local department state
        setDepartment(prev => prev ? { ...prev, ...advancedSettings } : null);
        return Promise.resolve();
      } else {
        throw new Error("Failed to update advanced settings");
      }
    } catch (error: any) {
      console.error("Error updating advanced settings:", error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-gray-600">Đang tải cài đặt phòng ban...</div>
        </div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy phòng ban</h2>
            <p className="text-gray-600 mb-4">Phòng ban bạn đang tìm kiếm không tồn tại.</p>
            <Link
              href="/dashboard/departments"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft size={16} className="mr-2" />
              Quay lại danh sách
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermissions={["system_departments_manage"]} fallbackPath="/dashboard">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/departments/${departmentId}`}
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} className="mr-2" />
              Quay lại
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Cài đặt phòng ban</h1>
              <p className="text-gray-600">{department.name} - {department.code}</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Settings */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Cài đặt chung</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số lượng thành viên tối đa
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="1000"
                    value={formData.maxMembers}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxMembers: parseInt(e.target.value) || 50 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                    Phòng ban đang hoạt động
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Member Management */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quản lý thành viên</h3>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="allowSelfJoin"
                    checked={formData.allowSelfJoin}
                    onChange={(e) => setFormData(prev => ({ ...prev, allowSelfJoin: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="allowSelfJoin" className="ml-2 block text-sm text-gray-900">
                    Cho phép thành viên tự tham gia
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="requireApproval"
                    checked={formData.requireApproval}
                    onChange={(e) => setFormData(prev => ({ ...prev, requireApproval: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="requireApproval" className="ml-2 block text-sm text-gray-900">
                    Yêu cầu phê duyệt khi tham gia
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="autoAssignPermissions"
                    checked={formData.autoAssignPermissions}
                    onChange={(e) => setFormData(prev => ({ ...prev, autoAssignPermissions: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="autoAssignPermissions" className="ml-2 block text-sm text-gray-900">
                    Tự động cấp quyền mặc định cho thành viên mới
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Default Permissions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quyền mặc định</h3>
              <p className="text-sm text-gray-600 mb-4">
                Các quyền này sẽ được cấp tự động cho thành viên mới khi tham gia phòng ban.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availablePermissions.map((permission) => (
                  <div key={permission} className="flex items-center">
                    <input
                      type="checkbox"
                      id={permission}
                      checked={formData.defaultPermissions.includes(permission)}
                      onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor={permission} className="ml-2 block text-sm text-gray-900">
                      {permission.replace('dept_', '').replace('_', ' ')}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Danger Zone */}
          <div className="bg-white rounded-lg shadow-sm border border-red-200">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <AlertTriangle size={20} className="text-red-600 mr-2" />
                <h3 className="text-lg font-semibold text-red-900">Vùng nguy hiểm</h3>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Xóa phòng ban</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    Xóa phòng ban sẽ loại bỏ tất cả thành viên và không thể khôi phục.
                  </p>
                  <button
                    type="button"
                    onClick={() => toast.info("Chức năng xóa phòng ban sẽ được phát triển")}
                    className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Xóa phòng ban
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Đang lưu...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  Lưu cài đặt
                </>
              )}
            </button>
          </div>
        </form>

        {/* Advanced Settings */}
        <div className="mt-8">
          <AdvancedSettings
            departmentId={departmentId}
            department={department}
            onUpdate={handleAdvancedSettingsUpdate}
          />
        </div>
      </div>
    </PermissionGuard>
  );
}
