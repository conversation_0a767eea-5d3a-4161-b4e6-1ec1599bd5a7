const mongoose = require('mongoose');
const User = require('../server/api/models/user');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/blog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Make user admin
const makeUserAdmin = async (identifier) => {
  try {
    // Find user by username or email
    const user = await User.findOne({
      $or: [
        { username: identifier },
        { email: identifier }
      ]
    });

    if (!user) {
      console.log(`User not found: ${identifier}`);
      return;
    }

    // Update user role to admin
    user.rule = 'admin';
    await user.save();

    console.log(`✅ User ${user.username} (${user.email}) is now an admin!`);
    console.log(`User ID: ${user._id}`);
    console.log(`Role: ${user.rule}`);

  } catch (error) {
    console.error('Error making user admin:', error);
  }
};

// Give user specific permissions
const giveUserPermissions = async (identifier, permissions) => {
  try {
    // Find user by username or email
    const user = await User.findOne({
      $or: [
        { username: identifier },
        { email: identifier }
      ]
    });

    if (!user) {
      console.log(`User not found: ${identifier}`);
      return;
    }

    // Update user permissions
    user.permissions = permissions;
    await user.save();

    console.log(`✅ User ${user.username} (${user.email}) permissions updated!`);
    console.log(`User ID: ${user._id}`);
    console.log(`Role: ${user.rule}`);
    console.log(`Permissions: ${permissions.join(', ')}`);

  } catch (error) {
    console.error('Error updating user permissions:', error);
  }
};

// List all users
const listUsers = async () => {
  try {
    const users = await User.find({}).select('_id username email rule permissions createdAt');
    console.log('\n📋 All Users:');
    console.log('=====================================');
    users.forEach(user => {
      console.log(`ID: ${user._id}`);
      console.log(`Username: ${user.username}`);
      console.log(`Email: ${user.email}`);
      console.log(`Role: ${user.rule}`);
      console.log(`Permissions: ${user.permissions ? user.permissions.join(', ') : 'None'}`);
      console.log(`Created: ${user.createdAt}`);
      console.log('-------------------------------------');
    });
  } catch (error) {
    console.error('Error listing users:', error);
  }
};

// Main function
const main = async () => {
  await connectDB();

  const args = process.argv.slice(2);
  const command = args[0];
  const identifier = args[1];

  if (command === 'list') {
    await listUsers();
  } else if (command === 'make-admin' && identifier) {
    await makeUserAdmin(identifier);
  } else if (command === 'give-permissions' && identifier) {
    // Default permissions for testing
    const defaultPermissions = [
      'user_view',
      'user_add',
      'user_edit',
      'file_view',
      'system_settings_view'
    ];
    await giveUserPermissions(identifier, defaultPermissions);
  } else if (command === 'give-all-permissions' && identifier) {
    // All available permissions
    const allPermissions = [
      'user_view',
      'user_add',
      'user_edit',
      'user_delete',
      'user_import_csv',
      'file_view',
      'file_upload',
      'file_delete',
      'system_settings_view',
      'system_settings_edit',
      'analytics_view',
      'permissions_manage'
    ];
    await giveUserPermissions(identifier, allPermissions);
  } else {
    console.log('Usage:');
    console.log('  node scripts/make-admin.js list                           # List all users');
    console.log('  node scripts/make-admin.js make-admin <username>          # Make user admin by username');
    console.log('  node scripts/make-admin.js make-admin <email>             # Make user admin by email');
    console.log('  node scripts/make-admin.js give-permissions <username>    # Give basic permissions');
    console.log('  node scripts/make-admin.js give-all-permissions <username> # Give all permissions');
  }

  mongoose.connection.close();
};

main().catch(console.error);
