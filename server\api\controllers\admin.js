const mongoose = require("mongoose");
const moment = require("moment");
const defineAbilityFor = require("../permissions/abilities");
const { ForbiddenError } = require("@casl/ability");

// Recent Activities
exports.getRecentActivities = async (req, res) => {
  try {
    const ability = defineAbilityFor(req.user);
    
    // Check if user has permission to view dashboard
    if (!ability.can("read", "dashboard")) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Since we removed analytics, return mock data for recent activities
    const activities = [
      {
        id: "mock1",
        type: 'page_view',
        page: "Trang chủ",
        path: "/",
        postTitle: null,
        postSlug: null,
        timestamp: new Date(),
        userAgent: null,
        ip: null
      },
      {
        id: "mock2", 
        type: 'page_view',
        page: "Về chúng tôi",
        path: "/about",
        postTitle: null,
        postSlug: null,
        timestamp: new Date(Date.now() - 60000),
        userAgent: null,
        ip: null
      }
    ];

    res.json({
      success: true,
      activities
    });

  } catch (error) {
    console.error('Error getting recent activities:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};
