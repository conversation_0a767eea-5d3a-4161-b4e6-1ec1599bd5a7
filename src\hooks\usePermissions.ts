import { useAppContext } from "@/app/app-provider";

export const usePermissions = () => {
  const { user } = useAppContext();

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;

    // System admin always has all permissions
    if (user.systemRole === "admin") return true;

    // Check if user has specific permission
    if (user.permissions?.includes(permission)) return true;

    // For department-specific permissions, check if user is department admin
    if (permission.startsWith('dept_') && user.isDepartmentAdmin && user.department) {
      return true;
    }

    // Legacy support: check old rule-based permissions
    if (user.rule === "admin") return true;

    return false;
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user) return false;

    // System admin always has all permissions
    if (user.systemRole === "admin") return true;

    // Check if user has any of the specified permissions
    if (permissions.some(permission => user.permissions?.includes(permission))) {
      return true;
    }

    // For department-specific permissions, check if user is department admin
    const deptPermissions = permissions.filter(p => p.startsWith('dept_'));
    if (deptPermissions.length > 0 && user.isDepartmentAdmin && user.department) {
      return true;
    }

    // Legacy support: check old rule-based permissions
    if (user.rule === "admin") return true;

    return false;
  };

  const getAllPermissions = (): string[] => {
    if (!user) return [];

    // System admin has all permissions
    if (user.systemRole === "admin" || user.rule === "admin") {
      return [
        // System Management
        "system_admin_full_access",
        "system_departments_manage",
        "system_users_manage",
        "system_settings_manage",
        "system_analytics_view",



        // Legacy File Management
        "file_view",
        "file_upload",
        "file_delete",

        // Legacy System Settings
        "system_settings_view",
        "system_settings_edit",

        // Legacy Analytics
        "analytics_view",

        // Legacy Permission Management
        "permissions_manage",

        // Department permissions
        "dept_file_view",
        "dept_file_upload",
        "dept_file_delete",
        "dept_analytics_view"
      ];
    }

    return user.permissions || [];
  };

  return {
    hasPermission,
    hasAnyPermission,
    getAllPermissions,
    userPermissions: user?.permissions || [],
    isAdmin: user?.systemRole === "admin" || user?.rule === "admin",
    isSystemAdmin: user?.systemRole === "admin",
    isDepartmentAdmin: user?.isDepartmentAdmin || false,
    userDepartment: user?.department || null
  };
};
