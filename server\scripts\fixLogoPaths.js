const mongoose = require('mongoose');
const Setting = require('../api/models/setting');
const Media = require('../api/models/media');
const { normalizeWebPath } = require('../api/utils/pathUtils');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/blog', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Fix logo paths in settings
const fixSettingLogoPaths = async () => {
  try {
    console.log('🔧 Fixing logo paths in settings...');

    const settings = await Setting.find({});
    console.log(`Found ${settings.length} settings`);
    let fixedCount = 0;

    for (const setting of settings) {
      console.log('Setting ID:', setting._id);
      console.log('Logo object:', setting.logo);

      if (setting.logo && setting.logo.path) {
        const originalPath = setting.logo.path;
        const normalizedPath = normalizeWebPath(originalPath);

        console.log(`Original: "${originalPath}"`);
        console.log(`Normalized: "${normalizedPath}"`);
        console.log(`Are different: ${originalPath !== normalizedPath}`);

        if (originalPath !== normalizedPath) {
          console.log(`📝 Fixing: ${originalPath} → ${normalizedPath}`);
          setting.logo.path = normalizedPath;
          setting.markModified('logo');
          await setting.save();
          fixedCount++;
          console.log('✅ Saved successfully');
        } else {
          console.log('⏭️ Path already normalized');
        }
      } else {
        console.log('⚠️ No logo or logo path found');
      }
    }

    console.log(`✅ Fixed ${fixedCount} setting logo paths`);
  } catch (error) {
    console.error('❌ Error fixing setting logo paths:', error);
  }
};

// Fix media paths
const fixMediaPaths = async () => {
  try {
    console.log('🔧 Fixing media paths...');
    
    const mediaItems = await Media.find({});
    let fixedCount = 0;
    
    for (const media of mediaItems) {
      if (media.path) {
        const originalPath = media.path;
        const normalizedPath = normalizeWebPath(originalPath);
        
        if (originalPath !== normalizedPath) {
          console.log(`📝 Fixing: ${originalPath} → ${normalizedPath}`);
          media.path = normalizedPath;
          await media.save();
          fixedCount++;
        }
      }
    }
    
    console.log(`✅ Fixed ${fixedCount} media paths`);
  } catch (error) {
    console.error('❌ Error fixing media paths:', error);
  }
};

// Main function
const main = async () => {
  console.log('🚀 Starting logo path fix script...');
  
  await connectDB();
  
  await fixSettingLogoPaths();
  await fixMediaPaths();
  
  console.log('✅ Logo path fix completed!');
  process.exit(0);
};

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = { fixSettingLogoPaths, fixMediaPaths };
