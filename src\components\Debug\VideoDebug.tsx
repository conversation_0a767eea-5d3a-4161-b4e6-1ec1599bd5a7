"use client";

import { useEffect, useState } from 'react';
import { refreshAllVideos } from '@/utils/videoUtils';

const VideoDebug = () => {
  const [videoInfo, setVideoInfo] = useState<any[]>([]);
  const [refreshCount, setRefreshCount] = useState(0);

  const scanVideos = () => {
    const videos = document.querySelectorAll('video');
    const info = Array.from(videos).map((video, index) => ({
      index,
      src: video.src,
      controls: video.hasAttribute('controls'),
      preload: video.getAttribute('preload'),
      classes: Array.from(video.classList),
      readyState: video.readyState,
      networkState: video.networkState,
      error: video.error?.message || null,
      parentElement: video.parentElement?.tagName,
      style: {
        display: video.style.display,
        maxWidth: video.style.maxWidth,
        height: video.style.height,
      }
    }));
    
    setVideoInfo(info);
    console.log('Video scan results:', info);
  };

  const handleRefresh = () => {
    const result = refreshAllVideos();
    setRefreshCount(prev => prev + 1);
    setTimeout(scanVideos, 500);
    console.log('Refresh result:', result);
  };

  useEffect(() => {
    scanVideos();
    
    // Scan every 2 seconds
    const interval = setInterval(scanVideos, 2000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-4 bg-gray-100 rounded mb-4 max-h-96 overflow-auto">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold">Video Debug Info:</h3>
        <div className="flex gap-2">
          <button 
            onClick={scanVideos}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
          >
            🔍 Scan Videos
          </button>
          <button 
            onClick={handleRefresh}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            🔄 Refresh Videos ({refreshCount})
          </button>
        </div>
      </div>
      
      <div className="mb-2">
        <strong>Total Videos Found:</strong> {videoInfo.length}
      </div>
      
      {videoInfo.length === 0 ? (
        <div className="text-red-600">❌ No video elements found in DOM</div>
      ) : (
        <div className="space-y-2">
          {videoInfo.map((video, idx) => (
            <div key={idx} className="border p-2 rounded bg-white">
              <div><strong>Video #{video.index}:</strong></div>
              <div><strong>Src:</strong> {video.src || '❌ No src'}</div>
              <div><strong>Controls:</strong> {video.controls ? '✅' : '❌'}</div>
              <div><strong>Preload:</strong> {video.preload || 'none'}</div>
              <div><strong>Classes:</strong> {video.classes.join(', ') || 'none'}</div>
              <div><strong>Ready State:</strong> {video.readyState} (0=empty, 1=metadata, 2=current, 3=future, 4=enough)</div>
              <div><strong>Network State:</strong> {video.networkState} (0=empty, 1=idle, 2=loading, 3=no_source)</div>
              <div><strong>Error:</strong> {video.error || 'none'}</div>
              <div><strong>Parent:</strong> {video.parentElement}</div>
              <div><strong>Style:</strong> display:{video.style.display}, maxWidth:{video.style.maxWidth}, height:{video.style.height}</div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-4 p-2 bg-yellow-100 rounded">
        <strong>Debug Actions:</strong>
        <div className="mt-1">
          <button 
            onClick={() => {
              const prosemirror = document.querySelector('.ProseMirror');
              console.log('ProseMirror content:', prosemirror?.innerHTML);
            }}
            className="mr-2 px-2 py-1 bg-yellow-500 text-white rounded text-xs"
          >
            Log ProseMirror HTML
          </button>
          <button 
            onClick={() => {
              const videos = document.querySelectorAll('video');
              videos.forEach(v => v.load());
              console.log('Forced reload on all videos');
            }}
            className="mr-2 px-2 py-1 bg-orange-500 text-white rounded text-xs"
          >
            Force Video Reload
          </button>
          <button 
            onClick={() => {
              const videos = document.querySelectorAll('video');
              videos.forEach(v => {
                v.style.border = '2px solid red';
                setTimeout(() => v.style.border = '', 2000);
              });
            }}
            className="px-2 py-1 bg-red-500 text-white rounded text-xs"
          >
            Highlight Videos
          </button>
        </div>
      </div>
    </div>
  );
};

export default VideoDebug;
