import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  dot?: boolean;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
  dot = false,
}) => {
  const baseClasses = 'inline-flex items-center font-medium rounded-full';

  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    danger: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800',
    secondary: 'bg-purple-100 text-purple-800',
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  const dotClasses = {
    default: 'bg-gray-500',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    danger: 'bg-red-500',
    info: 'bg-blue-500',
    secondary: 'bg-purple-500',
  };

  return (
    <span
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${className}
      `}
    >
      {dot && (
        <span
          className={`w-2 h-2 rounded-full mr-2 ${dotClasses[variant]}`}
        />
      )}
      {children}
    </span>
  );
};

// Status Badge Component
interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'approved' | 'rejected';
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  className = '',
}) => {
  const statusConfig = {
    active: {
      label: 'Hoạt động',
      variant: 'success' as const,
      dot: true,
    },
    inactive: {
      label: 'Không hoạt động',
      variant: 'secondary' as const,
      dot: true,
    },
    pending: {
      label: 'Chờ duyệt',
      variant: 'warning' as const,
      dot: true,
    },
    approved: {
      label: 'Đã duyệt',
      variant: 'success' as const,
      dot: true,
    },
    rejected: {
      label: 'Từ chối',
      variant: 'danger' as const,
      dot: true,
    },
  };

  const config = statusConfig[status];

  return (
    <Badge
      variant={config.variant}
      dot={config.dot}
      className={className}
    >
      {config.label}
    </Badge>
  );
};

// Role Badge Component
interface RoleBadgeProps {
  role: 'admin' | 'manager' | 'user' | 'editor';
  className?: string;
}

export const RoleBadge: React.FC<RoleBadgeProps> = ({
  role,
  className = '',
}) => {
  const roleConfig = {
    admin: {
      label: 'Quản trị viên',
      variant: 'danger' as const,
    },
    manager: {
      label: 'Quản lý',
      variant: 'info' as const,
    },
    editor: {
      label: 'Biên tập viên',
      variant: 'secondary' as const,
    },
    user: {
      label: 'Người dùng',
      variant: 'default' as const,
    },
  };

  const config = roleConfig[role];

  return (
    <Badge
      variant={config.variant}
      className={className}
    >
      {config.label}
    </Badge>
  );
};

// Count Badge Component
interface CountBadgeProps {
  count: number;
  max?: number;
  className?: string;
  variant?: 'default' | 'danger';
}

export const CountBadge: React.FC<CountBadgeProps> = ({
  count,
  max = 99,
  className = '',
  variant = 'danger',
}) => {
  const displayCount = count > max ? `${max}+` : count.toString();

  if (count === 0) return null;

  return (
    <Badge
      variant={variant}
      size="sm"
      className={`absolute -top-2 -right-2 min-w-[1.25rem] h-5 flex items-center justify-center ${className}`}
    >
      {displayCount}
    </Badge>
  );
};

// Priority Badge Component
interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high' | 'urgent';
  className?: string;
}

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({
  priority,
  className = '',
}) => {
  const priorityConfig = {
    low: {
      label: 'Thấp',
      variant: 'default' as const,
    },
    medium: {
      label: 'Trung bình',
      variant: 'warning' as const,
    },
    high: {
      label: 'Cao',
      variant: 'info' as const,
    },
    urgent: {
      label: 'Khẩn cấp',
      variant: 'danger' as const,
    },
  };

  const config = priorityConfig[priority];

  return (
    <Badge
      variant={config.variant}
      className={className}
    >
      {config.label}
    </Badge>
  );
};
