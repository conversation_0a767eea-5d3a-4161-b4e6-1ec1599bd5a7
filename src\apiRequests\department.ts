import http from "@/lib/http";

export interface Department {
  _id: string;
  name: string;
  description: string;
  code: string;
  defaultPermissions: string[];
  isActive: boolean;
  departmentAdmin?: {
    _id: string;
    username: string;
    email: string;
  };
  maxMembers: number;
  parentDepartment?: string;
  level: number;
  memberCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface DepartmentMember {
  _id: string;
  username: string;
  email: string;
  isDepartmentAdmin: boolean;
  permissions: string[];
  createdAt: string;
}

export interface CreateDepartmentRequest {
  name: string;
  description?: string;
  code: string;
  defaultPermissions?: string[];
  maxMembers?: number;
  parentDepartment?: string;
}

export interface UpdateDepartmentRequest {
  name?: string;
  description?: string;
  defaultPermissions?: string[];
  maxMembers?: number;
  isActive?: boolean;
}

export interface AssignAdminRequest {
  departmentId: string;
  userId: string;
}

export interface DepartmentListResponse {
  success: boolean;
  departments: Department[];
  total: number;
  page: number;
  perPage: number;
}

export interface DepartmentResponse {
  success: boolean;
  department: Department & {
    members?: DepartmentMember[];
  };
}

export interface DepartmentMembersResponse {
  success: boolean;
  members: DepartmentMember[];
  total: number;
  department: {
    _id: string;
    name: string;
    code: string;
  };
}

const departmentApiRequest = {
  // Get all departments
  getAllDepartments: (data: { page?: number; perPage?: number; includeDeleted?: boolean }, sessionToken: string) =>
    http.post<DepartmentListResponse>("/api/administrator/departments", data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get single department
  getDepartment: (id: string, sessionToken: string) =>
    http.get<DepartmentResponse>(`/api/administrator/departments/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Create department
  createDepartment: (data: CreateDepartmentRequest, sessionToken: string) =>
    http.post<DepartmentResponse>("/api/administrator/departments/create", data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Update department
  updateDepartment: (id: string, data: UpdateDepartmentRequest, sessionToken: string) =>
    http.put<DepartmentResponse>(`/api/administrator/departments/${id}`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Assign department admin
  assignDepartmentAdmin: (data: AssignAdminRequest, sessionToken: string) =>
    http.post<{ success: boolean; message: string }>("/api/administrator/departments/assign-admin", data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Get department members
  getDepartmentMembers: (id: string, params: { page?: number; perPage?: number }, sessionToken: string) => {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.perPage) queryParams.append('perPage', params.perPage.toString());

    const url = `/api/administrator/departments/${id}/members${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    return http.get<DepartmentMembersResponse>(url, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    });
  },

  // Search available users for department
  searchAvailableUsers: (departmentId: string, searchTerm: string, sessionToken: string) =>
    http.get<{ success: boolean; users: any[] }>(`/api/administrator/departments/${departmentId}/available-users?search=${encodeURIComponent(searchTerm)}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Add members to department
  addMembers: (id: string, data: { userIds: string[] }, sessionToken: string) =>
    http.post<{ success: boolean; message: string }>(`/api/administrator/departments/${id}/add-members`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Remove member from department
  removeMember: (id: string, userId: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(`/api/administrator/departments/${id}/members/${userId}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Create new member for department
  createMember: (id: string, data: {
    username: string;
    email: string;
    password: string;
    isDepartmentAdmin?: boolean;
    permissions?: string[];
  }, sessionToken: string) =>
    http.post<{ success: boolean; message: string; user?: any }>(`/api/administrator/departments/${id}/create-member`, data, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Delete department (soft delete)
  deleteDepartment: (id: string, sessionToken: string) =>
    http.delete<{ success: boolean; message: string }>(`/api/administrator/departments/${id}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // Restore deleted department
  restoreDepartment: (id: string, sessionToken: string) =>
    http.post<{ success: boolean; message: string; department?: Department }>(`/api/administrator/departments/${id}/restore`, {}, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),
};

export default departmentApiRequest;
