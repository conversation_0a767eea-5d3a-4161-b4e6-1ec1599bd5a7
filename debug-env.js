#!/usr/bin/env node

// Load environment variables
require('dotenv').config();

console.log('🔍 Environment Variables Debug Report');
console.log('=====================================');

console.log('\n📋 Current Environment:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PWD:', process.cwd());

console.log('\n🌐 Frontend URLs:');
console.log('NEXT_PUBLIC_API_ENDPOINT:', process.env.NEXT_PUBLIC_API_ENDPOINT);
console.log('NEXT_PUBLIC_URL:', process.env.NEXT_PUBLIC_URL);
console.log('Main_URL:', process.env.Main_URL);

console.log('\n🔧 Backend Config:');
console.log('MONGO_URL:', process.env.MONGO_URL);
console.log('PORT:', process.env.PORT);
console.log('CORS_ORIGINS:', process.env.CORS_ORIGINS);

console.log('\n🔐 Security:');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '***SET***' : 'NOT SET');
console.log('CRYPTOJS_SECRECT:', process.env.CRYPTOJS_SECRECT ? '***SET***' : 'NOT SET');

console.log('\n📁 File Check:');
const fs = require('fs');
const envExists = fs.existsSync('.env');
console.log('.env file exists:', envExists);

if (envExists) {
  const envContent = fs.readFileSync('.env', 'utf8');
  console.log('\n📄 .env file content preview:');
  const lines = envContent.split('\n').slice(0, 10);
  lines.forEach((line, i) => {
    if (line.trim() && !line.startsWith('#')) {
      const [key] = line.split('=');
      console.log(`${i + 1}: ${key}=***`);
    } else {
      console.log(`${i + 1}: ${line}`);
    }
  });
}

console.log('\n✅ Debug complete!');
