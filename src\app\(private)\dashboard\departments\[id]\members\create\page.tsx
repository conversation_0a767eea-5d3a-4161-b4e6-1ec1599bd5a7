"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { toast } from "react-toastify";
import Link from "next/link";
import { ArrowLeft, UserPlus, Eye, EyeOff, Save } from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";
import PermissionGuard from "@/components/PermissionGuard";

interface CreateMemberForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  isDepartmentAdmin: boolean;
  permissions: string[];
}

export default function CreateDepartmentMemberPage() {
  const params = useParams();
  const router = useRouter();
  const [department, setDepartment] = useState<Department | null>(null);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState<CreateMemberForm>({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    isDepartmentAdmin: false,
    permissions: []
  });

  const departmentId = params.id as string;

  const availablePermissions = [
    "dept_posts_view",
    "dept_posts_create", 
    "dept_posts_edit",
    "dept_posts_delete",
    "dept_files_view",
    "dept_files_upload",
    "dept_files_delete",
    "dept_analytics_view",
    "dept_members_view",
    "dept_members_manage",
  ];

  const fetchDepartment = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.getDepartment(departmentId, sessionToken);
      
      if (response && response.payload && response.payload.success) {
        const dept = response.payload.department;
        setDepartment(dept);
        // Set default permissions from department
        setFormData(prev => ({
          ...prev,
          permissions: dept.defaultPermissions || []
        }));
      } else {
        toast.error("Không thể tải thông tin phòng ban");
        router.push("/dashboard/departments");
      }
    } catch (error: any) {
      console.error("Error fetching department:", error);
      toast.error("Có lỗi xảy ra khi tải thông tin phòng ban");
      router.push("/dashboard/departments");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
    }
  }, [departmentId]);

  const handleInputChange = (field: keyof CreateMemberForm, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => p !== permission)
    }));
  };

  const generatePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setFormData(prev => ({
      ...prev,
      password,
      confirmPassword: password
    }));
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      toast.error("Vui lòng nhập tên người dùng");
      return false;
    }
    if (!formData.email.trim()) {
      toast.error("Vui lòng nhập email");
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast.error("Email không hợp lệ");
      return false;
    }
    if (!formData.password) {
      toast.error("Vui lòng nhập mật khẩu");
      return false;
    }
    if (formData.password.length < 6) {
      toast.error("Mật khẩu phải có ít nhất 6 ký tự");
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      toast.error("Mật khẩu xác nhận không khớp");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setCreating(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const response = await departmentApiRequest.createMember(
        departmentId,
        {
          username: formData.username.trim(),
          email: formData.email.trim(),
          password: formData.password,
          isDepartmentAdmin: formData.isDepartmentAdmin,
          permissions: formData.permissions
        },
        sessionToken
      );
      
      if (response && response.payload && response.payload.success) {
        toast.success("Tạo thành viên thành công!");
        router.push(`/dashboard/departments/${departmentId}/members`);
      } else {
        toast.error("Không thể tạo thành viên. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error creating member:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi tạo thành viên";
      toast.error(errorMessage);
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-gray-600">Đang tải...</div>
        </div>
      </div>
    );
  }

  if (!department) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Không tìm thấy phòng ban</h2>
            <p className="text-gray-600 mb-4">Phòng ban bạn đang tìm kiếm không tồn tại.</p>
            <Link
              href="/dashboard/departments"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowLeft size={16} className="mr-2" />
              Quay lại danh sách
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard requiredPermissions={["system_departments_manage", "dept_members_manage"]} fallbackPath="/dashboard">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/departments/${departmentId}/members`}
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} className="mr-2" />
              Quay lại
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tạo thành viên mới</h1>
              <p className="text-gray-600">{department.name} - {department.code}</p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="max-w-2xl">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông tin cơ bản</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tên người dùng <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Nhập tên người dùng"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Nhập email"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Password */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Mật khẩu</h3>
                <button
                  type="button"
                  onClick={generatePassword}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Tạo mật khẩu tự động
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mật khẩu <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Nhập mật khẩu"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Xác nhận mật khẩu <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Nhập lại mật khẩu"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Role & Permissions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Vai trò và quyền</h3>
              
              <div className="mb-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isDepartmentAdmin"
                    checked={formData.isDepartmentAdmin}
                    onChange={(e) => handleInputChange('isDepartmentAdmin', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isDepartmentAdmin" className="ml-2 block text-sm text-gray-900">
                    Quản lý phòng ban
                  </label>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Quản lý phòng ban có quyền quản lý tất cả thành viên và tài nguyên trong phòng ban
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Quyền cụ thể</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {availablePermissions.map((permission) => (
                    <div key={permission} className="flex items-center">
                      <input
                        type="checkbox"
                        id={permission}
                        checked={formData.permissions.includes(permission)}
                        onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={permission} className="ml-2 block text-sm text-gray-900">
                        {permission.replace('dept_', '').replace('_', ' ')}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link
              href={`/dashboard/departments/${departmentId}/members`}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Hủy
            </Link>
            <button
              type="submit"
              disabled={creating}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {creating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Đang tạo...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  Tạo thành viên
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </PermissionGuard>
  );
}
