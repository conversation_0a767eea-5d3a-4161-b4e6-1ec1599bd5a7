import http from "@/lib/http";
import envConfig from "@/config";

export interface RecentActivity {
  id: string;
  type: 'page_view';
  page: string;
  path: string;
  postTitle?: string;
  postSlug?: string;
  timestamp: string;
  userAgent?: string;
  ip?: string;
}

export interface RecentActivitiesResponse {
  success: boolean;
  activities: RecentActivity[];
}

export interface PendingPost {
  _id: string;
  title: string;
  slug: string;
  short: string;
  createdAt: string;
  updatedAt: string;
  user: {
    _id: string;
    username: string;
    email: string;
  };
  categories: Array<{
    _id: string;
    name: string;
    slug: string;
  }>;
  isActive: boolean;
  isFeature: boolean;
  views: number;
}

export interface PendingPostsResponse {
  success: boolean;
  posts: PendingPost[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalPosts: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

const adminApiRequest = {
  getRecentActivities: (sessionToken: string, limit?: number) =>
    http.get<RecentActivitiesResponse>(`/api/administrator/recent-activities${limit ? `?limit=${limit}` : ''}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  toggleUserPrivate: (userId: string, sessionToken: string) =>
    http.put<{ success: boolean; user: any; message?: string }>("/api/administrator/update-private", {
      id: userId
    }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

};

export default adminApiRequest;
