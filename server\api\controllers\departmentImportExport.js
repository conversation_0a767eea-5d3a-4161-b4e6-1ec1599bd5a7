const mongoose = require("mongoose");
const Department = require("../models/department");
const User = require("../models/user");
const DepartmentFile = require("../models/departmentFile");
const DepartmentAnnouncement = require("../models/departmentAnnouncement");
const DepartmentReport = require("../models/departmentReport");
const { ForbiddenError } = require("@casl/ability");
const defineAbilityFor = require("../permissions/abilities");
const ExcelJS = require('exceljs');
const csv = require('csv-parser');
const fs = require('fs').promises;
const path = require('path');

// Export department members to Excel
exports.exportMembers = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { format = 'excel' } = req.query;

    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canExport = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId);

    if (!canExport) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Get department members
    const members = await User.find({ 
      department: departmentId,
      private: false 
    }).select('username email fullName phone position isDepartmentAdmin permissions isActive createdAt');

    if (format === 'excel') {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Department Members');

      // Add headers
      worksheet.columns = [
        { header: 'Tên đăng nhập', key: 'username', width: 20 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Họ tên', key: 'fullName', width: 25 },
        { header: 'Số điện thoại', key: 'phone', width: 15 },
        { header: 'Chức vụ', key: 'position', width: 20 },
        { header: 'Quản trị viên', key: 'isDepartmentAdmin', width: 15 },
        { header: 'Trạng thái', key: 'isActive', width: 12 },
        { header: 'Ngày tham gia', key: 'createdAt', width: 15 },
        { header: 'Quyền', key: 'permissions', width: 40 }
      ];

      // Add data
      members.forEach(member => {
        worksheet.addRow({
          username: member.username,
          email: member.email,
          fullName: member.fullName || '',
          phone: member.phone || '',
          position: member.position || '',
          isDepartmentAdmin: member.isDepartmentAdmin ? 'Có' : 'Không',
          isActive: member.isActive ? 'Hoạt động' : 'Không hoạt động',
          createdAt: member.createdAt.toLocaleDateString('vi-VN'),
          permissions: member.permissions ? member.permissions.join(', ') : ''
        });
      });

      // Style headers
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F3FF' }
      };

      // Set response headers
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="department-members-${department.code}-${Date.now()}.xlsx"`);

      await workbook.xlsx.write(res);
      res.end();

    } else if (format === 'csv') {
      const csvData = [
        ['Tên đăng nhập', 'Email', 'Họ tên', 'Số điện thoại', 'Chức vụ', 'Quản trị viên', 'Trạng thái', 'Ngày tham gia', 'Quyền'],
        ...members.map(member => [
          member.username,
          member.email,
          member.fullName || '',
          member.phone || '',
          member.position || '',
          member.isDepartmentAdmin ? 'Có' : 'Không',
          member.isActive ? 'Hoạt động' : 'Không hoạt động',
          member.createdAt.toLocaleDateString('vi-VN'),
          member.permissions ? member.permissions.join('; ') : ''
        ])
      ];

      const csvContent = csvData.map(row => row.map(field => `"${field}"`).join(',')).join('\n');

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="department-members-${department.code}-${Date.now()}.csv"`);
      res.write('\uFEFF'); // BOM for UTF-8
      res.end(csvContent);

    } else {
      return res.status(400).json({
        success: false,
        message: "Unsupported format. Use 'excel' or 'csv'"
      });
    }

  } catch (error) {
    console.error('Error exporting members:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Export department reports
exports.exportReports = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { format = 'excel', startDate, endDate } = req.query;

    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canExport = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId);

    if (!canExport) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    // Build query
    const query = { department: departmentId };
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const reports = await DepartmentReport.find(query)
      .populate('generatedBy', 'username email')
      .sort({ createdAt: -1 });

    if (format === 'excel') {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Department Reports');

      // Add headers
      worksheet.columns = [
        { header: 'Tiêu đề', key: 'title', width: 30 },
        { header: 'Loại báo cáo', key: 'reportType', width: 20 },
        { header: 'Người tạo', key: 'generatedBy', width: 20 },
        { header: 'Ngày tạo', key: 'createdAt', width: 15 },
        { header: 'Từ ngày', key: 'startDate', width: 15 },
        { header: 'Đến ngày', key: 'endDate', width: 15 },
        { header: 'Trạng thái', key: 'status', width: 12 },
        { header: 'Công khai', key: 'isPublic', width: 10 },
        { header: 'Tổng thành viên', key: 'totalMembers', width: 15 },
        { header: 'Thành viên hoạt động', key: 'activeMembers', width: 18 },
        { header: 'Tổng file', key: 'totalFiles', width: 12 },
        { header: 'Tổng thông báo', key: 'totalAnnouncements', width: 18 }
      ];

      // Add data
      reports.forEach(report => {
        worksheet.addRow({
          title: report.title,
          reportType: report.reportType,
          generatedBy: report.generatedBy?.username || '',
          createdAt: report.createdAt.toLocaleDateString('vi-VN'),
          startDate: report.reportPeriod?.startDate ? new Date(report.reportPeriod.startDate).toLocaleDateString('vi-VN') : '',
          endDate: report.reportPeriod?.endDate ? new Date(report.reportPeriod.endDate).toLocaleDateString('vi-VN') : '',
          status: report.status,
          isPublic: report.isPublic ? 'Có' : 'Không',
          totalMembers: report.data?.memberStats?.totalMembers || 0,
          activeMembers: report.data?.memberStats?.activeMembers || 0,
          totalFiles: report.data?.fileStats?.totalFiles || 0,
          totalAnnouncements: report.data?.activityStats?.announcementsCreated || 0
        });
      });

      // Style headers
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F3FF' }
      };

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="department-reports-${department.code}-${Date.now()}.xlsx"`);

      await workbook.xlsx.write(res);
      res.end();

    } else {
      return res.status(400).json({
        success: false,
        message: "Only Excel format is supported for reports"
      });
    }

  } catch (error) {
    console.error('Error exporting reports:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

// Export department data (comprehensive)
exports.exportDepartmentData = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { includeMembers = true, includeFiles = true, includeAnnouncements = true, includeReports = true } = req.query;

    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found"
      });
    }

    // Permission check
    const canExport = req.user.systemRole === 'admin' ||
                     (req.user.isDepartmentAdmin && req.user.department?.toString() === departmentId);

    if (!canExport) {
      return res.status(403).json({
        success: false,
        message: "Access denied"
      });
    }

    const workbook = new ExcelJS.Workbook();

    // Department Info Sheet
    const deptSheet = workbook.addWorksheet('Department Info');
    deptSheet.columns = [
      { header: 'Thuộc tính', key: 'property', width: 20 },
      { header: 'Giá trị', key: 'value', width: 40 }
    ];

    deptSheet.addRows([
      { property: 'Tên phòng ban', value: department.name },
      { property: 'Mã phòng ban', value: department.code },
      { property: 'Mô tả', value: department.description || '' },
      { property: 'Số thành viên tối đa', value: department.maxMembers },
      { property: 'Trạng thái', value: department.isActive ? 'Hoạt động' : 'Không hoạt động' },
      { property: 'Ngày tạo', value: department.createdAt.toLocaleDateString('vi-VN') },
      { property: 'Cập nhật lần cuối', value: department.updatedAt.toLocaleDateString('vi-VN') }
    ]);

    // Members Sheet
    if (includeMembers === 'true') {
      const members = await User.find({ 
        department: departmentId,
        private: false 
      }).select('username email fullName phone position isDepartmentAdmin isActive createdAt');

      const membersSheet = workbook.addWorksheet('Members');
      membersSheet.columns = [
        { header: 'Tên đăng nhập', key: 'username', width: 20 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Họ tên', key: 'fullName', width: 25 },
        { header: 'Số điện thoại', key: 'phone', width: 15 },
        { header: 'Chức vụ', key: 'position', width: 20 },
        { header: 'Quản trị viên', key: 'isDepartmentAdmin', width: 15 },
        { header: 'Trạng thái', key: 'isActive', width: 12 },
        { header: 'Ngày tham gia', key: 'createdAt', width: 15 }
      ];

      members.forEach(member => {
        membersSheet.addRow({
          username: member.username,
          email: member.email,
          fullName: member.fullName || '',
          phone: member.phone || '',
          position: member.position || '',
          isDepartmentAdmin: member.isDepartmentAdmin ? 'Có' : 'Không',
          isActive: member.isActive ? 'Hoạt động' : 'Không hoạt động',
          createdAt: member.createdAt.toLocaleDateString('vi-VN')
        });
      });
    }

    // Files Sheet
    if (includeFiles === 'true') {
      const files = await DepartmentFile.find({ 
        department: departmentId,
        status: 'active'
      }).populate('uploadedBy', 'username').sort({ uploadedAt: -1 });

      const filesSheet = workbook.addWorksheet('Files');
      filesSheet.columns = [
        { header: 'Tên file', key: 'originalName', width: 30 },
        { header: 'Loại', key: 'category', width: 15 },
        { header: 'Kích thước', key: 'fileSize', width: 12 },
        { header: 'Người tải lên', key: 'uploadedBy', width: 20 },
        { header: 'Ngày tải lên', key: 'uploadedAt', width: 15 },
        { header: 'Lượt xem', key: 'totalViews', width: 12 },
        { header: 'Lượt tải', key: 'totalDownloads', width: 12 }
      ];

      files.forEach(file => {
        filesSheet.addRow({
          originalName: file.originalName,
          category: file.category,
          fileSize: Math.round(file.fileSize / 1024) + ' KB',
          uploadedBy: file.uploadedBy?.username || '',
          uploadedAt: file.uploadedAt.toLocaleDateString('vi-VN'),
          totalViews: file.analytics?.totalViews || 0,
          totalDownloads: file.analytics?.totalDownloads || 0
        });
      });
    }

    // Style all headers
    workbook.worksheets.forEach(worksheet => {
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F3FF' }
      };
    });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="department-data-${department.code}-${Date.now()}.xlsx"`);

    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('Error exporting department data:', error);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
};

module.exports = {
  exportMembers: exports.exportMembers,
  exportReports: exports.exportReports,
  exportDepartmentData: exports.exportDepartmentData
};
