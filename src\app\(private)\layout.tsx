import type { <PERSON><PERSON><PERSON> } from "next";
import AppProvider from "@/app/app-provider";
import { SettingProvider } from "@/context/SettingContext";
import NextTopLoader from "nextjs-toploader";
import SideMenu from "@/components/SideMenu";
import FooterPrivate from "@/components/Widget/FooterPrivate";
import SecretHeader from "@/components/Navigation/SecretHeader";
import PermissionsDebug from "@/components/Debug/PermissionsDebug";

export default async function PrivateLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AppProvider>
      <SettingProvider>
        <div className="main-private bg-gray-50 min-h-screen">
          <SideMenu />
          <div className="relative md:ml-64 min-h-screen">
            <SecretHeader />
            <main className="relative">
              <div className="mx-auto w-full">
                <div className="px-6 py-8 md:px-8">
                  <div className="max-w-7xl mx-auto">
                    {children}
                  </div>
                </div>
                <FooterPrivate />
              </div>
            </main>
          </div>
          <NextTopLoader
            color="#3B82F6"
            height={3}
            showSpinner={false}
          />
          <PermissionsDebug />
        </div>
      </SettingProvider>
    </AppProvider>
  );
}
