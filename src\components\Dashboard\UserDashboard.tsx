"use client";

import PermissionGuard from "@/components/PermissionGuard";
import { usePermissions } from "@/hooks/usePermissions";
import { useAppContext } from "@/app/app-provider";
import { User, Settings, FileText, Calendar } from "react-feather";

export default function UserDashboard() {
  const { user } = useAppContext();
  const { hasPermission } = usePermissions();

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Ch<PERSON>o mừng, {user?.username}!
        </h1>
        <p className="text-gray-600">
          Đ<PERSON><PERSON> là bảng điều khiển cá nhân của bạn.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Account Settings */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center mb-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="ml-3 text-lg font-semibold text-gray-900">
              Thông tin tài khoản
            </h3>
          </div>
          <p className="text-gray-600 mb-4">
            Xem và chỉnh sửa thông tin cá nhân của bạn
          </p>
          <a
            href="/dashboard/account"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Chỉnh sửa
          </a>
        </div>

        {/* File Management - if user has permission */}
        {(hasPermission("file_view") || hasPermission("file_upload")) && (
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="ml-3 text-lg font-semibold text-gray-900">
                Quản lý File
              </h3>
            </div>
            <p className="text-gray-600 mb-4">
              Xem và quản lý các file của bạn
            </p>
            <a
              href="/dashboard/files"
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Xem File
            </a>
          </div>
        )}

        {/* Settings - if user has permission */}
        {(hasPermission("system_settings_view") || hasPermission("system_settings_edit")) && (
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Settings className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="ml-3 text-lg font-semibold text-gray-900">
                Cài đặt hệ thống
              </h3>
            </div>
            <p className="text-gray-600 mb-4">
              Xem cài đặt hệ thống
            </p>
            <a
              href="/dashboard/setting"
              className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              Xem cài đặt
            </a>
          </div>
        )}
      </div>

      {/* User Info Card */}
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Thông tin tài khoản
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tên người dùng
            </label>
            <p className="text-gray-900">{user?.username || 'Chưa cập nhật'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <p className="text-gray-900">{user?.email || 'Chưa cập nhật'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Vai trò
            </label>
            <p className="text-gray-900 capitalize">{user?.rule || 'user'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Số quyền được cấp
            </label>
            <p className="text-gray-900">{user?.permissions?.length || 0} quyền</p>
          </div>
        </div>
      </div>
    </div>
  );
}
