const mongoose = require("mongoose");
const { Schema } = mongoose;
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const departmentReportSchema = new Schema(
  {
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true
    },
    reportType: {
      type: String,
      enum: [
        "monthly_summary",
        "member_activity", 
        "performance_metrics",
        "file_usage",
        "custom_report"
      ],
      required: true
    },
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    reportPeriod: {
      startDate: {
        type: Date,
        required: true
      },
      endDate: {
        type: Date,
        required: true
      }
    },
    data: {
      // Member statistics
      memberStats: {
        totalMembers: { type: Number, default: 0 },
        activeMembers: { type: Number, default: 0 },
        newMembers: { type: Number, default: 0 },
        departmentAdmins: { type: Number, default: 0 }
      },
      
      // Activity statistics
      activityStats: {
        totalLogins: { type: Number, default: 0 },
        averageSessionTime: { type: Number, default: 0 },
        filesUploaded: { type: Number, default: 0 },
        filesDownloaded: { type: Number, default: 0 },
        announcementsCreated: { type: Number, default: 0 },
        announcementsViewed: { type: Number, default: 0 }
      },
      
      // Performance metrics
      performanceMetrics: {
        taskCompletionRate: { type: Number, default: 0 },
        averageResponseTime: { type: Number, default: 0 },
        userSatisfactionScore: { type: Number, default: 0 },
        productivityIndex: { type: Number, default: 0 }
      },
      
      // File usage statistics
      fileStats: {
        totalFiles: { type: Number, default: 0 },
        totalFileSize: { type: Number, default: 0 },
        mostAccessedFiles: [{
          fileName: String,
          accessCount: Number,
          lastAccessed: Date
        }],
        fileTypeDistribution: [{
          fileType: String,
          count: Number,
          totalSize: Number
        }]
      },
      
      // Custom data for flexible reporting
      customData: {
        type: Schema.Types.Mixed,
        default: {}
      }
    },
    
    // Report metadata
    generatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    generatedAt: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ["generating", "completed", "failed", "archived"],
      default: "generating"
    },
    isPublic: {
      type: Boolean,
      default: false
    },
    tags: [{
      type: String,
      trim: true
    }],
    
    // Export information
    exportFormats: [{
      format: {
        type: String,
        enum: ["pdf", "excel", "csv", "json"]
      },
      filePath: String,
      generatedAt: Date,
      fileSize: Number
    }],
    
    // Sharing and permissions
    sharedWith: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      permissions: [{
        type: String,
        enum: ["view", "download", "edit", "share"]
      }],
      sharedAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    // Comments and feedback
    comments: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      content: {
        type: String,
        required: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    // Scheduling for recurring reports
    schedule: {
      isRecurring: {
        type: Boolean,
        default: false
      },
      frequency: {
        type: String,
        enum: ["daily", "weekly", "monthly", "quarterly", "yearly"]
      },
      nextGeneration: Date,
      lastGeneration: Date
    }
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better performance
departmentReportSchema.index({ department: 1, reportType: 1 });
departmentReportSchema.index({ generatedBy: 1 });
departmentReportSchema.index({ status: 1 });
departmentReportSchema.index({ "reportPeriod.startDate": 1, "reportPeriod.endDate": 1 });
departmentReportSchema.index({ createdAt: -1 });
departmentReportSchema.index({ tags: 1 });

// Plugin for CASL permissions
departmentReportSchema.plugin(accessibleRecordsPlugin);

// Virtual for report duration
departmentReportSchema.virtual('reportDuration').get(function() {
  if (this.reportPeriod && this.reportPeriod.startDate && this.reportPeriod.endDate) {
    return Math.ceil((this.reportPeriod.endDate - this.reportPeriod.startDate) / (1000 * 60 * 60 * 24));
  }
  return 0;
});

// Virtual for total activity score
departmentReportSchema.virtual('totalActivityScore').get(function() {
  if (!this.data || !this.data.activityStats) return 0;
  
  const stats = this.data.activityStats;
  return (stats.totalLogins || 0) + 
         (stats.filesUploaded || 0) + 
         (stats.filesDownloaded || 0) + 
         (stats.announcementsCreated || 0);
});

// Static methods
departmentReportSchema.statics.findByDepartment = function(departmentId, options = {}) {
  const query = { department: departmentId };
  
  if (options.reportType) {
    query.reportType = options.reportType;
  }
  
  if (options.status) {
    query.status = options.status;
  }
  
  if (options.dateRange) {
    query['reportPeriod.startDate'] = { $gte: options.dateRange.start };
    query['reportPeriod.endDate'] = { $lte: options.dateRange.end };
  }
  
  return this.find(query)
    .populate('department', 'name code')
    .populate('generatedBy', 'username email')
    .sort({ createdAt: -1 });
};

departmentReportSchema.statics.findPublicReports = function(departmentId) {
  return this.find({ 
    department: departmentId, 
    isPublic: true, 
    status: 'completed' 
  })
  .populate('department', 'name code')
  .populate('generatedBy', 'username email')
  .sort({ createdAt: -1 });
};

// Instance methods
departmentReportSchema.methods.addComment = function(userId, content) {
  this.comments.push({
    user: userId,
    content: content
  });
  return this.save();
};

departmentReportSchema.methods.shareWith = function(userId, permissions = ['view']) {
  // Remove existing share if exists
  this.sharedWith = this.sharedWith.filter(
    share => !share.user.equals(userId)
  );
  
  // Add new share
  this.sharedWith.push({
    user: userId,
    permissions: permissions
  });
  
  return this.save();
};

departmentReportSchema.methods.canAccess = function(user) {
  // Department admin or system admin can access
  if (user.systemRole === 'admin' || 
      (user.isDepartmentAdmin && user.department.equals(this.department))) {
    return true;
  }
  
  // Report creator can access
  if (this.generatedBy.equals(user._id)) {
    return true;
  }
  
  // Check if shared with user
  const sharedEntry = this.sharedWith.find(
    share => share.user.equals(user._id)
  );
  
  if (sharedEntry) {
    return true;
  }
  
  // Check if public and user is in same department
  if (this.isPublic && user.department && user.department.equals(this.department)) {
    return true;
  }
  
  return false;
};

// Pre-save middleware
departmentReportSchema.pre('save', function(next) {
  // Validate report period
  if (this.reportPeriod && this.reportPeriod.startDate && this.reportPeriod.endDate) {
    if (this.reportPeriod.startDate >= this.reportPeriod.endDate) {
      return next(new Error('Start date must be before end date'));
    }
  }
  
  // Set next generation for recurring reports
  if (this.schedule && this.schedule.isRecurring && this.schedule.frequency) {
    if (!this.schedule.nextGeneration) {
      const next = new Date();
      switch (this.schedule.frequency) {
        case 'daily':
          next.setDate(next.getDate() + 1);
          break;
        case 'weekly':
          next.setDate(next.getDate() + 7);
          break;
        case 'monthly':
          next.setMonth(next.getMonth() + 1);
          break;
        case 'quarterly':
          next.setMonth(next.getMonth() + 3);
          break;
        case 'yearly':
          next.setFullYear(next.getFullYear() + 1);
          break;
      }
      this.schedule.nextGeneration = next;
    }
  }
  
  next();
});

module.exports = mongoose.model("DepartmentReport", departmentReportSchema);
