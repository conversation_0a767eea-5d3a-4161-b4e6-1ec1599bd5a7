/**
 * Utility functions for handling video elements in ProseMirror editor
 */

/**
 * Initialize video elements with proper attributes
 */
export const initializeVideoElements = (container: HTMLElement | Document = document) => {
  const videos = container.querySelectorAll('video');
  
  videos.forEach((video) => {
    // Force add all required classes
    video.classList.add('prosemirror-video', 'video-player');

    // Ensure video has proper attributes
    video.setAttribute('controls', 'true');
    video.setAttribute('preload', 'metadata');

    // Force proper styling
    video.style.maxWidth = '100%';
    video.style.height = 'auto';
    video.style.display = 'block';
    video.style.margin = '1rem 0';
    video.style.borderRadius = '8px';
    video.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
    video.style.background = '#000';

    // Force video to reload if needed
    if (video.readyState === 0 && video.src) {
      video.load();
    }

    // Add error handling (only once)
    if (!video.hasAttribute('data-initialized')) {
      video.setAttribute('data-initialized', 'true');

      video.addEventListener('error', (e) => {
        console.error('Video load error:', e);
      });

      video.addEventListener('loadedmetadata', () => {
        console.log('Video metadata loaded:', video.src);
      });
    }
  });
  
  return videos.length;
};

/**
 * Fix video elements that lost their ProseMirror classes
 */
export const fixVideoClasses = (container: HTMLElement | Document = document) => {
  const videos = container.querySelectorAll('video');
  let fixedCount = 0;
  
  videos.forEach((video) => {
    let needsUpdate = false;
    
    // Check if video is missing required classes
    if (!video.classList.contains('prosemirror-video')) {
      video.classList.add('prosemirror-video');
      needsUpdate = true;
    }
    
    if (!video.classList.contains('video-player')) {
      video.classList.add('video-player');
      needsUpdate = true;
    }
    
    // Check if video is missing required attributes
    if (!video.hasAttribute('controls')) {
      video.setAttribute('controls', 'true');
      needsUpdate = true;
    }
    
    if (!video.hasAttribute('preload')) {
      video.setAttribute('preload', 'metadata');
      needsUpdate = true;
    }
    
    if (needsUpdate) {
      fixedCount++;
    }
  });
  
  return fixedCount;
};

/**
 * Observer to watch for video elements being added to DOM
 */
export const createVideoObserver = (callback?: (videos: NodeListOf<HTMLVideoElement>) => void) => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          
          // Check if the added node is a video
          if (element.tagName === 'VIDEO') {
            initializeVideoElements(element.parentElement || document);
            if (callback) {
              callback(document.querySelectorAll('video'));
            }
          }
          
          // Check if the added node contains videos
          const videos = element.querySelectorAll?.('video');
          if (videos && videos.length > 0) {
            initializeVideoElements(element);
            if (callback) {
              callback(videos);
            }
          }
        }
      });
    });
  });
  
  return observer;
};

/**
 * Start observing for video elements
 */
export const startVideoObserver = (container: HTMLElement = document.body) => {
  const observer = createVideoObserver((videos) => {
    console.log(`Initialized ${videos.length} video elements`);
  });
  
  observer.observe(container, {
    childList: true,
    subtree: true,
  });
  
  return observer;
};

/**
 * Force refresh all videos on page
 */
export const refreshAllVideos = () => {
  const count = initializeVideoElements();
  const fixedCount = fixVideoClasses();

  console.log(`Refreshed ${count} videos, fixed ${fixedCount} videos`);

  return { count, fixedCount };
};

/**
 * Normalize video data to consistent format
 */
export const normalizeVideoData = (videoData: any): string[] => {
  if (!videoData) return [];

  if (typeof videoData === 'string') {
    return [videoData];
  }

  if (Array.isArray(videoData)) {
    return videoData.map(item => {
      if (typeof item === 'string') {
        return item;
      } else if (typeof item === 'object' && item !== null) {
        // Handle object format like {"0": "url"}
        if (item['0']) {
          return item['0'];
        } else {
          // Get first value from object
          const values = Object.values(item);
          return values[0] || '';
        }
      }
      return '';
    }).filter(url => url && typeof url === 'string'); // Remove empty strings and non-strings
  }

  return [];
};

/**
 * Get first video URL from video data
 */
export const getFirstVideoUrl = (videoData: any): string => {
  const normalized = normalizeVideoData(videoData);
  return normalized[0] || '';
};

/**
 * Check if video data is valid
 */
export const isValidVideoData = (videoData: any): boolean => {
  const normalized = normalizeVideoData(videoData);
  return normalized.length > 0;
};
