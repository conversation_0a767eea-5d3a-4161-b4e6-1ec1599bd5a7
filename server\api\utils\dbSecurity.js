/**
 * Database Security Configuration
 * Contains security best practices for MongoDB connections and queries
 */

const mongoose = require('mongoose');

// Security configurations
const DB_SECURITY_CONFIG = {
  // Connection security options
  connectionOptions: {
    // Enable SSL in production
    ssl: process.env.NODE_ENV === 'production',
    // Connection timeout
    serverSelectionTimeoutMS: 30000,
    // Socket timeout
    socketTimeoutMS: 45000,
    // Buffer max entries
    bufferMaxEntries: 0,
    // Use new URL parser
    useNewUrlParser: true,
    useUnifiedTopology: true,
    // Additional security options
    authSource: 'admin',
    retryWrites: true,
    w: 'majority'
  },
  
  // Query security options
  queryOptions: {
    // Disable auto-indexing in production
    autoIndex: process.env.NODE_ENV !== 'production',
    // Set strict mode
    strict: true,
    // Enable validation
    runValidators: true,
    // Set read concern
    readConcern: { level: 'majority' }
  }
};

// Secure connection function
const createSecureConnection = (connectionString, options = {}) => {
  const secureOptions = {
    ...DB_SECURITY_CONFIG.connectionOptions,
    ...options
  };
  
  // Validate connection string
  if (!connectionString || typeof connectionString !== 'string') {
    throw new Error('Invalid database connection string');
  }
  
  // Remove any potential injection characters
  const sanitizedConnectionString = connectionString.replace(/[<>\"']/g, '');
  
  return mongoose.connect(sanitizedConnectionString, secureOptions);
};

// Query sanitization helpers
const sanitizeQuery = (query) => {
  if (typeof query !== 'object' || query === null) {
    return {};
  }
  
  const sanitized = {};
  for (const key in query) {
    if (query.hasOwnProperty(key)) {
      // Remove potentially dangerous operators
      if (key.startsWith('$')) {
        continue;
      }
      
      const value = query[key];
      
      // Handle nested objects
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        sanitized[key] = sanitizeQuery(value);
      } else {
        sanitized[key] = value;
      }
    }
  }
  
  return sanitized;
};

// Safe find with pagination and limits
const safeFindWithPagination = async (Model, query = {}, options = {}) => {
  const {
    page = 1,
    limit = 10,
    sort = { createdAt: -1 },
    select,
    populate
  } = options;
  
  // Sanitize query
  const sanitizedQuery = sanitizeQuery(query);
  
  // Apply limits
  const safeLimit = Math.min(Math.max(parseInt(limit), 1), 100); // Max 100 items
  const safePage = Math.max(parseInt(page), 1);
  const skip = (safePage - 1) * safeLimit;
  
  // Build query
  let queryBuilder = Model.find(sanitizedQuery)
    .limit(safeLimit)
    .skip(skip)
    .sort(sort);
  
  if (select) {
    queryBuilder = queryBuilder.select(select);
  }
  
  if (populate) {
    queryBuilder = queryBuilder.populate(populate);
  }
  
  // Execute query with timeout
  const results = await queryBuilder.maxTimeMS(30000).exec();
  
  // Get total count for pagination
  const total = await Model.countDocuments(sanitizedQuery).maxTimeMS(30000);
  
  return {
    data: results,
    pagination: {
      page: safePage,
      limit: safeLimit,
      total,
      totalPages: Math.ceil(total / safeLimit),
      hasNext: safePage < Math.ceil(total / safeLimit),
      hasPrev: safePage > 1
    }
  };
};

// Safe aggregation with limits
const safeAggregate = async (Model, pipeline = [], options = {}) => {
  const { maxTimeMS = 30000, allowDiskUse = false } = options;
  
  // Add security stages to pipeline
  const securePipeline = [
    // Limit initial documents
    { $limit: 100000 },
    ...pipeline,
    // Limit final results
    { $limit: 1000 }
  ];
  
  return await Model.aggregate(securePipeline)
    .maxTimeMS(maxTimeMS)
    .allowDiskUse(allowDiskUse)
    .exec();
};

// Schema security helpers
const addSecurityToSchema = (schema) => {
  // Add indexes for common query patterns
  schema.index({ createdAt: -1 });
  schema.index({ updatedAt: -1 });
  
  // Add version key for optimistic locking
  schema.set('versionKey', '__v');
  
  // Enable strict mode
  schema.set('strict', true);
  
  // Disable auto-indexing in production
  schema.set('autoIndex', process.env.NODE_ENV !== 'production');
  
  // Add validation
  schema.set('runValidators', true);
  
  // Add timestamp if not exists
  if (!schema.paths.createdAt && !schema.paths.updatedAt) {
    schema.add({
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now }
    });
  }
  
  // Add pre-save middleware to update timestamps
  schema.pre('save', function(next) {
    if (this.isModified() && !this.isNew) {
      this.updatedAt = new Date();
    }
    next();
  });
  
  // Add pre-update middleware
  schema.pre(['updateOne', 'findOneAndUpdate'], function(next) {
    this.set({ updatedAt: new Date() });
    next();
  });
  
  return schema;
};

module.exports = {
  DB_SECURITY_CONFIG,
  createSecureConnection,
  sanitizeQuery,
  safeFindWithPagination,
  safeAggregate,
  addSecurityToSchema
};
