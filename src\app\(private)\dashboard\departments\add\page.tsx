"use client";

import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import DepartmentForm from "@/components/Department/DepartmentForm";
import PermissionGuard from "@/components/PermissionGuard";
import departmentApiRequest, { CreateDepartmentRequest } from "@/apiRequests/department";

export default function AddDepartmentPage() {
  const router = useRouter();

  const handleCreate = async (data: CreateDepartmentRequest) => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await departmentApiRequest.createDepartment(data, sessionToken);
      
      if (result.payload.success) {
        toast.success("Tạo phòng ban thành công!");
        router.push(`/dashboard/departments/${result.payload.department._id}`);
      } else {
        toast.error("Không thể tạo phòng ban. Vui lòng thử lại.");
      }
    } catch (error: any) {
      console.error("Error creating department:", error);
      const errorMessage = error?.payload?.message || "Có lỗi xảy ra khi tạo phòng ban";
      toast.error(errorMessage);
    }
  };

  return (
    <PermissionGuard requiredPermissions={["system_departments_manage"]} fallbackPath="/dashboard">
      <div className="container mx-auto px-4 py-6">
        <DepartmentForm onSubmit={handleCreate} />
      </div>
    </PermissionGuard>
  );
}
