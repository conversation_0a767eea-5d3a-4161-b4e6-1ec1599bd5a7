const mongoose = require("mongoose");
const { Schema } = mongoose;
const { accessibleRecordsPlugin } = require("@casl/mongoose");

const departmentAnnouncementSchema = new Schema(
  {
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true
    },
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200
    },
    content: {
      type: String,
      required: true,
      trim: true
    },
    summary: {
      type: String,
      trim: true,
      maxlength: 500
    },
    type: {
      type: String,
      enum: [
        "general",      // Thông báo chung
        "urgent",       // Khẩn cấp
        "meeting",      // Họp
        "deadline",     // Hạn chót
        "policy",       // <PERSON><PERSON>h sách
        "event",        // S<PERSON> kiện
        "training",     // Đào tạo
        "maintenance"   // Bảo trì
      ],
      default: "general"
    },
    priority: {
      type: String,
      enum: ["low", "normal", "high", "urgent"],
      default: "normal"
    },
    status: {
      type: String,
      enum: ["draft", "published", "archived", "expired"],
      default: "draft"
    },
    
    // Publishing settings
    publishedAt: {
      type: Date
    },
    expiresAt: {
      type: Date
    },
    scheduledFor: {
      type: Date
    },
    
    // Author information
    author: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true
    },
    lastEditedBy: {
      type: Schema.Types.ObjectId,
      ref: "User"
    },
    
    // Targeting
    targetAudience: {
      type: String,
      enum: ["all", "admins", "members", "specific"],
      default: "all"
    },
    specificTargets: [{
      type: Schema.Types.ObjectId,
      ref: "User"
    }],
    
    // Attachments
    attachments: [{
      fileName: {
        type: String,
        required: true
      },
      originalName: {
        type: String,
        required: true
      },
      fileSize: {
        type: Number,
        required: true
      },
      mimeType: {
        type: String,
        required: true
      },
      filePath: {
        type: String,
        required: true
      },
      uploadedAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    // Interaction tracking
    views: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      viewedAt: {
        type: Date,
        default: Date.now
      },
      ipAddress: String,
      userAgent: String
    }],
    
    likes: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      likedAt: {
        type: Date,
        default: Date.now
      }
    }],
    
    comments: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true
      },
      content: {
        type: String,
        required: true,
        trim: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      },
      editedAt: {
        type: Date
      },
      isEdited: {
        type: Boolean,
        default: false
      }
    }],
    
    // Settings
    allowComments: {
      type: Boolean,
      default: true
    },
    allowLikes: {
      type: Boolean,
      default: true
    },
    requireAcknowledgment: {
      type: Boolean,
      default: false
    },
    
    // Acknowledgments (for important announcements)
    acknowledgments: [{
      user: {
        type: Schema.Types.ObjectId,
        ref: "User"
      },
      acknowledgedAt: {
        type: Date,
        default: Date.now
      },
      ipAddress: String
    }],
    
    // Notification settings
    sendNotification: {
      type: Boolean,
      default: true
    },
    notificationSent: {
      type: Boolean,
      default: false
    },
    notificationSentAt: {
      type: Date
    },
    
    // Tags for categorization
    tags: [{
      type: String,
      trim: true
    }],
    
    // SEO and metadata
    slug: {
      type: String,
      unique: true,
      sparse: true
    },
    metaDescription: {
      type: String,
      maxlength: 160
    },
    
    // Analytics
    analytics: {
      totalViews: {
        type: Number,
        default: 0
      },
      uniqueViews: {
        type: Number,
        default: 0
      },
      totalLikes: {
        type: Number,
        default: 0
      },
      totalComments: {
        type: Number,
        default: 0
      },
      acknowledgmentRate: {
        type: Number,
        default: 0
      }
    }
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better performance
departmentAnnouncementSchema.index({ department: 1, status: 1 });
departmentAnnouncementSchema.index({ author: 1 });
departmentAnnouncementSchema.index({ publishedAt: -1 });
departmentAnnouncementSchema.index({ expiresAt: 1 });
departmentAnnouncementSchema.index({ type: 1, priority: 1 });
departmentAnnouncementSchema.index({ tags: 1 });
departmentAnnouncementSchema.index({ slug: 1 });
departmentAnnouncementSchema.index({ "views.user": 1 });

// Plugin for CASL permissions
departmentAnnouncementSchema.plugin(accessibleRecordsPlugin);

// Virtual for checking if announcement is active
departmentAnnouncementSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === 'published' && 
         (!this.expiresAt || this.expiresAt > now);
});

// Virtual for checking if announcement is expired
departmentAnnouncementSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt <= new Date();
});

// Virtual for time remaining until expiry
departmentAnnouncementSchema.virtual('timeUntilExpiry').get(function() {
  if (!this.expiresAt) return null;
  const now = new Date();
  const diff = this.expiresAt.getTime() - now.getTime();
  return diff > 0 ? diff : 0;
});

// Static methods
departmentAnnouncementSchema.statics.findByDepartment = function(departmentId, options = {}) {
  const query = { department: departmentId };
  
  if (options.status) {
    query.status = options.status;
  } else {
    // Default to published announcements
    query.status = 'published';
  }
  
  if (options.type) {
    query.type = options.type;
  }
  
  if (options.priority) {
    query.priority = options.priority;
  }
  
  if (options.tags && options.tags.length > 0) {
    query.tags = { $in: options.tags };
  }
  
  if (options.activeOnly) {
    query.status = 'published';
    const now = new Date();
    query.$or = [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: now } }
    ];
  }
  
  return this.find(query)
    .populate('author', 'username email')
    .populate('lastEditedBy', 'username email')
    .sort({ priority: -1, publishedAt: -1 });
};

departmentAnnouncementSchema.statics.findActiveAnnouncements = function(departmentId) {
  const now = new Date();
  return this.find({
    department: departmentId,
    status: 'published',
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: now } }
    ]
  })
  .populate('author', 'username email')
  .sort({ priority: -1, publishedAt: -1 });
};

// Instance methods
departmentAnnouncementSchema.methods.addView = function(userId, ipAddress, userAgent) {
  // Check if user already viewed this announcement
  const existingView = this.views.find(view => 
    view.user && view.user.toString() === userId.toString()
  );
  
  if (!existingView) {
    this.views.push({
      user: userId,
      ipAddress,
      userAgent
    });
    this.analytics.uniqueViews += 1;
  }
  
  this.analytics.totalViews += 1;
  return this.save();
};

departmentAnnouncementSchema.methods.addLike = function(userId) {
  // Check if user already liked
  const existingLike = this.likes.find(like => 
    like.user.toString() === userId.toString()
  );
  
  if (existingLike) {
    return { success: false, message: "Already liked" };
  }
  
  this.likes.push({ user: userId });
  this.analytics.totalLikes += 1;
  return this.save().then(() => ({ success: true, message: "Liked" }));
};

departmentAnnouncementSchema.methods.removeLike = function(userId) {
  const likeIndex = this.likes.findIndex(like => 
    like.user.toString() === userId.toString()
  );
  
  if (likeIndex === -1) {
    return { success: false, message: "Not liked" };
  }
  
  this.likes.splice(likeIndex, 1);
  this.analytics.totalLikes = Math.max(0, this.analytics.totalLikes - 1);
  return this.save().then(() => ({ success: true, message: "Unliked" }));
};

departmentAnnouncementSchema.methods.addComment = function(userId, content) {
  if (!this.allowComments) {
    throw new Error("Comments are not allowed for this announcement");
  }
  
  this.comments.push({
    user: userId,
    content: content.trim()
  });
  this.analytics.totalComments += 1;
  return this.save();
};

departmentAnnouncementSchema.methods.acknowledge = function(userId, ipAddress) {
  // Check if user already acknowledged
  const existingAck = this.acknowledgments.find(ack => 
    ack.user.toString() === userId.toString()
  );
  
  if (existingAck) {
    return { success: false, message: "Already acknowledged" };
  }
  
  this.acknowledgments.push({
    user: userId,
    ipAddress
  });
  
  // Update acknowledgment rate
  this.updateAcknowledgmentRate();
  
  return this.save().then(() => ({ success: true, message: "Acknowledged" }));
};

departmentAnnouncementSchema.methods.updateAcknowledgmentRate = function() {
  if (this.requireAcknowledgment) {
    // This would need to be calculated based on department member count
    // For now, we'll use a simple calculation
    const totalAcknowledgments = this.acknowledgments.length;
    const totalViews = this.analytics.uniqueViews || 1;
    this.analytics.acknowledgmentRate = (totalAcknowledgments / totalViews) * 100;
  }
};

// Pre-save middleware
departmentAnnouncementSchema.pre('save', function(next) {
  // Generate slug if not provided
  if (!this.slug && this.title) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }
  
  // Auto-generate summary if not provided
  if (!this.summary && this.content) {
    this.summary = this.content.substring(0, 200) + (this.content.length > 200 ? '...' : '');
  }
  
  // Set published date when status changes to published
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  // Update analytics
  this.analytics.totalLikes = this.likes.length;
  this.analytics.totalComments = this.comments.length;
  
  next();
});

module.exports = mongoose.model("DepartmentAnnouncement", departmentAnnouncementSchema);
