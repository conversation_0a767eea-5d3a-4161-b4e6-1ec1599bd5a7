"use client";

import { useAppContext } from "@/app/app-provider";
import { usePermissions } from "@/hooks/usePermissions";

export default function UserPermissionsDebug() {
  const { user } = useAppContext();
  const { hasPermission, isAdmin, userPermissions } = usePermissions();

  const settingsPermissions = [
    "system_settings_view",
    "system_settings_edit"
  ];

  return (
    <div className="bg-yellow-100 border border-yellow-400 p-4 rounded mb-4">
      <h3 className="font-bold text-lg mb-2">🐛 User Permissions Debug</h3>
      
      <div className="space-y-2">
        <div>
          <strong>User Email:</strong> {user?.email || 'Not logged in'}
        </div>
        
        <div>
          <strong>User Rule:</strong> {user?.rule || 'None'}
        </div>
        
        <div>
          <strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>User Permissions Array:</strong> 
          <pre className="bg-gray-100 p-2 rounded mt-1 text-xs">
            {JSON.stringify(userPermissions, null, 2)}
          </pre>
        </div>
        
        <div>
          <strong>Settings Permissions Check:</strong>
          <ul className="ml-4 mt-1">
            {settingsPermissions.map(permission => (
              <li key={permission}>
                {permission}: {hasPermission(permission) ? '✅ Yes' : '❌ No'}
              </li>
            ))}
          </ul>
        </div>

        <div>
          <strong>Raw User Object:</strong>
          <pre className="bg-gray-100 p-2 rounded mt-1 text-xs max-h-40 overflow-y-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
