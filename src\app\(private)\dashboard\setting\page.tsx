"use client";

import EditForm from "@/app/(private)/dashboard/setting/edit-from";
import PermissionGuard from "@/components/PermissionGuard";
import { usePermissions } from "@/hooks/usePermissions";

export default function Setting() {
  const { hasPermission } = usePermissions();
  const canEdit = hasPermission("system_settings_edit");

  return (
    <PermissionGuard requiredPermissions={["system_settings_view", "system_settings_edit"]} requireAll={false}>
      <h1 className="text-2xl mb-4">Cài đặt</h1>
      
      {!canEdit && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <strong>Lưu ý:</strong> Bạn chỉ có quyền xem cài đặt. <PERSON><PERSON> chỉnh sửa, cần liên hệ quản trị viên cấp quyền "Chỉnh sửa cài đặt hệ thống".
        </div>
      )}
      
      {canEdit ? (
        <EditForm />
      ) : (
        <div className="opacity-60 pointer-events-none">
          <EditForm />
        </div>
      )}
    </PermissionGuard>
  );
}
