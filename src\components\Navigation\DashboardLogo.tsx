import Image from "next/image";
import Link from "next/link";
import { useSetting } from "@/context/SettingContext";
import { useAuth } from "@/hooks/useAuth";
import envConfig from "@/config";

const DashboardLogo = () => {
  const { setting, loading } = useSetting();
  const { hasPermission } = useAuth();

  // All users go to dashboard
  const dashboardUrl = "/dashboard";

  if (loading) {
    return (
      <div className="flex items-center space-x-3">
        <div className="h-12 w-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <Link href={dashboardUrl} className="flex items-center space-x-3 rtl:space-x-reverse hover:opacity-80 transition-opacity">
      {setting?.logo?.path ? (
        <Image
          src={`${envConfig.NEXT_PUBLIC_API_ENDPOINT}/${setting.logo.path}`}
          alt="Site Logo"
          width={300}
          height={200}
          quality={100}
          priority={true}
          className="max-w-32 max-h-12 object-contain"
        />
      ) : (
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">D</span>
          </div>
          <span className="text-lg font-bold text-gray-800">Dashboard</span>
        </div>
      )}
    </Link>
  );
};

export default DashboardLogo;
