"use client";
import { useState, useEffect } from "react";
import { Card, ActionCard } from "@/components/ui/Card";
import adminApiRequest, { RecentActivity } from "@/apiRequests/admin";
import { toast } from "react-toastify";
import { useAppContext } from "@/app/app-provider";
import { usePermissions } from "@/hooks/usePermissions";
import UserDashboard from "@/components/Dashboard/UserDashboard";

import {
  FileText,
  Calendar,
  Settings,
  Plus,
  Activity,
  Shield,
  Home
} from "react-feather";

// Helper functions
const getTimeAgo = (dateString: string) => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return `${diffInSeconds} giây trước`;
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
  return `${Math.floor(diffInSeconds / 2592000)} tháng trước`;
};

const getActivityType = (type: string) => {
  switch (type) {
    case 'post_created':
      return { color: 'bg-green-500', icon: 'create' };
    case 'post_updated':
      return { color: 'bg-blue-500', icon: 'update' };
    case 'post_pending':
      return { color: 'bg-orange-500', icon: 'pending' };
    case 'post_pending_updated':
      return { color: 'bg-orange-400', icon: 'pending' };
    case 'page_created':
      return { color: 'bg-green-500', icon: 'create' };
    case 'page_updated':
      return { color: 'bg-blue-500', icon: 'update' };
    default:
      return { color: 'bg-gray-500', icon: 'default' };
  }
};

const AdminDashboard = () => {
  const { hasPermission } = usePermissions();
  
  // Nếu user không có quyền thống kê, hiển thị UserDashboard
  if (!hasPermission("dept_analytics_view") && !hasPermission("system_analytics_view")) {
    return <UserDashboard />;
  }

  const [sessionToken, setSessionToken] = useState<string>("");
  const [activities, setActivities] = useState<RecentActivity[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(true);

  const { user } = useAppContext();

  useEffect(() => {
    // Get session token on client side only
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  // Fetch recent activities
  useEffect(() => {
    if (!sessionToken) return;

    const fetchRecentActivities = async () => {
      try {
        setActivitiesLoading(true);
        const result = await adminApiRequest.getRecentActivities(sessionToken, 8);
        if (result.payload.success) {
          setActivities(result.payload.activities);
        } else {
          toast.error("Không thể tải hoạt động gần đây");
        }
      } catch (error: any) {
        console.error("Error fetching recent activities:", error);
        if (error?.status === 429) {
          toast.warning("Quá nhiều yêu cầu, vui lòng thử lại sau");
        } else {
          toast.error("Lỗi khi tải hoạt động gần đây");
        }
      } finally {
        setActivitiesLoading(false);
      }
    };

    const timeoutId = setTimeout(fetchRecentActivities, 1500);
    return () => clearTimeout(timeoutId);
  }, [sessionToken]);

  const quickActions = [
    {
      title: "Quản lý phòng ban",
      description: "Xem và quản lý các phòng ban",
      href: "/dashboard/departments",
      icon: <Home size={20} />,
      color: "green" as const,
    },
    {
      title: "Cài đặt hệ thống",
      description: "Cấu hình và tùy chỉnh website",
      href: "/dashboard/setting",
      icon: <Settings size={20} />,
      color: "orange" as const,
    },
    {
      title: "Quản lý file",
      description: "Quản lý tài liệu và hình ảnh",
      href: "/dashboard/files",
      icon: <FileText size={20} />,
      color: "blue" as const,
    },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl text-white p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 flex items-center">
              <Shield className="mr-3" size={36} />
              Bảng điều khiển quản trị
            </h1>
            <p className="text-purple-100 text-lg">
              Quản lý toàn bộ hệ thống và nội dung website
            </p>
          </div>
          <div className="hidden md:block">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <Activity size={48} className="text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Thao tác nhanh</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <ActionCard
              key={index}
              title={action.title}
              description={action.description}
              href={action.href}
              icon={action.icon}
              color={action.color}
            />
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Hoạt động gần đây</h3>
          <a href="#" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            Xem tất cả
          </a>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {activitiesLoading ? (
            // Loading skeleton
            Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100 animate-pulse">
                <div className="w-2 h-2 rounded-full bg-gray-200"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))
          ) : activities.length > 0 ? (
            activities.map((activity) => {
              const timeAgo = getTimeAgo(activity.timestamp);
              const activityType = getActivityType(activity.type);

              return (
                <div key={activity.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 border border-gray-100">
                  <div className={`w-2 h-2 rounded-full ${activityType.color}`}></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.page}</p>
                    <p className="text-xs text-gray-500">
                      {activity.postTitle && (
                        <span className="font-medium">{activity.postTitle}</span>
                      )}
                      {activity.postTitle && " • "}
                      {timeAgo}
                    </p>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-span-full text-center py-8 text-gray-500">
              <Activity size={48} className="mx-auto mb-2 opacity-50" />
              <p>Chưa có hoạt động nào</p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AdminDashboard;
