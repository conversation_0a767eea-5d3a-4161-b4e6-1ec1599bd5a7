import z from 'zod'

export const FileItemSchema = z.object({
  _id: z.string(),
  filename: z.string(),
  originalName: z.string(),
  mimetype: z.string(),
  size: z.number(),
  path: z.string(),
  url: z.string(),
  uploadedBy: z.object({
    _id: z.string(),
    username: z.string(),
    email: z.string().optional()
  }),
  uploadedAt: z.string(),
  type: z.enum(['image', 'video', 'document', 'other']),
  isActive: z.boolean().default(true),
  tags: z.array(z.string()).optional(),
  description: z.string().optional()
})

export const FileResSchema = z.object({
  success: z.boolean(),
  files: z.array(FileItemSchema),
  total: z.number().optional(),
  page: z.number().optional(),
  perPage: z.number().optional(),
  message: z.string().optional()
})

export const FileDeleteSchema = z.object({
  success: z.boolean(),
  message: z.string()
})

export const FileUploadSchema = z.object({
  success: z.boolean(),
  file: FileItemSchema,
  message: z.string().optional()
})

export const FileSearchSchema = z.object({
  query: z.string().optional(),
  type: z.enum(['image', 'video', 'document', 'other', 'all']).optional(),
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['uploadedAt', 'filename', 'size']).default('uploadedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

export const FileBulkActionSchema = z.object({
  fileIds: z.array(z.string()),
  action: z.enum(['delete', 'activate', 'deactivate'])
})

export type FileItemType = z.TypeOf<typeof FileItemSchema>
export type FileResType = z.TypeOf<typeof FileResSchema>
export type FileDeleteType = z.TypeOf<typeof FileDeleteSchema>
export type FileUploadType = z.TypeOf<typeof FileUploadSchema>
export type FileSearchType = z.TypeOf<typeof FileSearchSchema>
export type FileBulkActionType = z.TypeOf<typeof FileBulkActionSchema>
