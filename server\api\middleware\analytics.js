const { PageView, DailyAnalytics } = require('../models/analytics');
const crypto = require('crypto');

// Generate session ID from IP and User Agent
const generateSessionId = (ip, userAgent) => {
  const today = new Date().toDateString();
  return crypto.createHash('md5').update(`${ip}-${userAgent}-${today}`).digest('hex');
};

// Determine page type from path
const getPageType = (path) => {
  // Frontend routes
  if (path === '/' || path === '/home') return 'home';
  if (path === '/about') return 'about';
  if (path === '/contact') return 'contact';

  return 'other';
};

// Extract post ID from blog path or API path
const extractPostId = (path) => {
  // For frontend routes like /blog/[slug]
  const frontendMatch = path.match(/^\/blog\/([a-f\d]{24})$/);
  if (frontendMatch) return frontendMatch[1];

  // For API routes like /api/post/[slug]
  const apiMatch = path.match(/^\/api\/post\/([^\/]+)$/);
  if (apiMatch && apiMatch[1] !== 'home' && apiMatch[1] !== 'all') {
    return apiMatch[1]; // This is the slug, we'll need to resolve to ID later
  }

  return null;
};

// Middleware to track page views
const trackPageView = async (req, res, next) => {
  try {
    const path = req.path;
    console.log(`🔍 Analytics middleware checking: ${req.method} ${path}`);

    // Only track specific routes
    const shouldTrack = (
      // Frontend routes
      path === '/' ||
      path === '/home'
    ) && req.method === 'GET';

    if (!shouldTrack) {
      console.log(`⏭️ Skipping tracking for: ${path}`);
      return next();
    }

    console.log(`📊 Tracking page view: ${path}`);

    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    const referer = req.get('Referer') || '';
    const sessionId = generateSessionId(ip, userAgent);
    
    const pageType = getPageType(path);
    let postId = null;

    // Extract post ID for blog posts
    if (pageType === 'post') {
      const extractedId = extractPostId(path);
      if (extractedId) {
        // If it's an API route with slug, we need to resolve slug to ID
        if (path.startsWith('/api/post/') && extractedId.length !== 24) {
          // This is a slug, resolve it to ObjectId
          try {
            const Post = require('../models/post');
            const post = await Post.findOne({ slug: extractedId }).select('_id');
            postId = post ? post._id.toString() : null;
          } catch (error) {
            console.log('Error resolving slug to ID:', error.message);
            postId = null;
          }
        } else {
          // This is already an ObjectId
          postId = extractedId;
        }
      }
    }

    // Create page view record
    const pageView = new PageView({
      page: pageType,
      path: path,
      postId: postId,
      userAgent: userAgent,
      ip: ip,
      referer: referer,
      sessionId: sessionId
    });

    // Save asynchronously to not block response
    pageView.save().catch(err => {
      console.error('Error saving page view:', err);
    });

    // Update daily analytics asynchronously
    updateDailyAnalytics(pageType, postId, sessionId).catch(err => {
      console.error('Error updating daily analytics:', err);
    });

    // If this is a post view, increment post views counter
    if (pageType === 'post' && postId) {
      incrementPostViews(postId).catch(err => {
        console.error('Error incrementing post views:', err);
      });
    }

  } catch (error) {
    console.error('Analytics tracking error:', error);
  }
  
  next();
};

// Update daily analytics
const updateDailyAnalytics = async (pageType, postId, sessionId) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  try {
    let updateData = { $inc: { totalViews: 1 } };
    
    if (pageType === 'home') {
      updateData.$inc.homeViews = 1;
    } else if (pageType === 'post') {
      updateData.$inc.postViews = 1;
    }

    // Update or create daily analytics
    await DailyAnalytics.findOneAndUpdate(
      { date: today },
      updateData,
      { upsert: true, new: true }
    );

    // Update unique visitors count (approximate)
    const todayStart = new Date(today);
    const todayEnd = new Date(today);
    todayEnd.setDate(todayEnd.getDate() + 1);

    const uniqueSessionsToday = await PageView.distinct('sessionId', {
      timestamp: { $gte: todayStart, $lt: todayEnd }
    });

    await DailyAnalytics.findOneAndUpdate(
      { date: today },
      { uniqueVisitors: uniqueSessionsToday.length }
    );

    // Update top posts if this is a post view
    if (pageType === 'post' && postId) {
      await DailyAnalytics.findOneAndUpdate(
        { date: today, 'topPosts.postId': postId },
        { $inc: { 'topPosts.$.views': 1 } }
      );

      // If post not in topPosts, add it
      const analytics = await DailyAnalytics.findOne({ date: today });
      if (analytics && !analytics.topPosts.some(p => p.postId.toString() === postId)) {
        await DailyAnalytics.findOneAndUpdate(
          { date: today },
          { $push: { topPosts: { postId: postId, views: 1 } } }
        );
      }
    }

  } catch (error) {
    console.error('Error updating daily analytics:', error);
  }
};

// Increment post views counter in Post model
const incrementPostViews = async (postIdOrSlug) => {
  try {
    const Post = require('../models/post');

    let post;
    // If it's a 24-character hex string, it's an ObjectId
    if (postIdOrSlug && postIdOrSlug.length === 24 && /^[a-f\d]{24}$/i.test(postIdOrSlug)) {
      post = await Post.findByIdAndUpdate(
        postIdOrSlug,
        { $inc: { views: 1 } },
        { new: true }
      );
    } else {
      // It's a slug, find by slug and increment
      post = await Post.findOneAndUpdate(
        { slug: postIdOrSlug },
        { $inc: { views: 1 } },
        { new: true }
      );
    }

    if (post) {
      console.log(`📈 Incremented views for post: ${post.title} (${post.views} views)`);
    }
  } catch (error) {
    console.error('Error incrementing post views:', error);
  }
};

module.exports = {
  trackPageView,
  generateSessionId,
  getPageType,
  extractPostId
};
