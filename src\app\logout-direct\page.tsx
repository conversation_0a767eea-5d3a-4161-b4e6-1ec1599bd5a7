'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import authApiRequest from '@/apiRequests/auth'
import { useAppContext } from '@/app/app-provider'

export default function LogoutDirectPage() {
  const router = useRouter()
  const { setUser } = useAppContext()

  useEffect(() => {
    const performLogout = async () => {
      try {
        // Clear local storage
        localStorage.removeItem("user");
        localStorage.removeItem('sessionToken')
        localStorage.removeItem('sessionTokenExpiresAt')
        
        // Clear app state
        setUser(null)
        
        // Force logout API call
        await authApiRequest.logoutFromNextClientToNextServer(true)
        
        // Redirect to login with logout flag
        window.location.href = '/login?logout=true'
        
      } catch (error) {
        console.error('Direct logout error:', error)
        
        // Even if API fails, clear everything and redirect
        localStorage.removeItem("user");
        localStorage.removeItem('sessionToken')
        localStorage.removeItem('sessionTokenExpiresAt')
        setUser(null)
        
        window.location.href = '/login?force=true'
      }
    }

    performLogout()
  }, [router, setUser])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="loading loading-spinner loading-lg"></div>
        <p className="mt-4">Đang đăng xuất...</p>
      </div>
    </div>
  )
}
